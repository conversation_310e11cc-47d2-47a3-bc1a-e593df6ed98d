package com.mdmusfikurrahaman.learngermanapp

import android.content.Context
import android.util.Log
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdView
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.MobileAds
import com.google.android.gms.ads.initialization.InitializationStatus
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener

class AdManager(private val context: Context) {
    
    companion object {
        private const val TAG = "AdManager"
        // Your real banner ad unit ID from AdMob
        private const val BANNER_AD_UNIT_ID = "ca-app-pub-2281902770675036/1560909931"
        // Test ad unit ID for development
        private const val TEST_BANNER_AD_UNIT_ID = "ca-app-pub-3940256099942544/6300978111"
    }

    fun initializeAds(onComplete: (() -> Unit)? = null) {
        MobileAds.initialize(context) { initializationStatus ->
            Log.d(TAG, "AdMob initialized: ${initializationStatus.adapterStatusMap}")
            onComplete?.invoke()
        }
    }

    fun loadBannerAd(adView: AdView, isDebug: Boolean = false) {
        // Use test ad unit ID in debug mode, real ad unit ID in release
        val adUnitId = if (isDebug) TEST_BANNER_AD_UNIT_ID else BANNER_AD_UNIT_ID

        Log.d(TAG, "🎯 AdManager.loadBannerAd called")
        Log.d(TAG, "🎯 Debug mode: $isDebug")
        Log.d(TAG, "🎯 Using ad unit ID: $adUnitId")
        Log.d(TAG, "🎯 AdView size: ${adView.adSize}")

        val adRequest = AdRequest.Builder().build()

        adView.adListener = object : AdListener() {
            override fun onAdClicked() {
                Log.d(TAG, "🎯 Banner ad clicked")
            }

            override fun onAdClosed() {
                Log.d(TAG, "🎯 Banner ad closed")
            }

            override fun onAdFailedToLoad(adError: LoadAdError) {
                Log.e(TAG, "❌ Banner ad failed to load: ${adError.message}")
                Log.e(TAG, "❌ Error code: ${adError.code}")
                Log.e(TAG, "❌ Error domain: ${adError.domain}")
                Log.e(TAG, "❌ Response info: ${adError.responseInfo}")

                // Common error codes and solutions
                when (adError.code) {
                    0 -> Log.e(TAG, "❌ ERROR_CODE_INTERNAL_ERROR - Internal error")
                    1 -> Log.e(TAG, "❌ ERROR_CODE_INVALID_REQUEST - Invalid request")
                    2 -> Log.e(TAG, "❌ ERROR_CODE_NETWORK_ERROR - Network error")
                    3 -> Log.e(TAG, "❌ ERROR_CODE_NO_FILL - No ad to show")
                    else -> Log.e(TAG, "❌ Unknown error code: ${adError.code}")
                }
            }

            override fun onAdImpression() {
                Log.d(TAG, "✅ Banner ad impression recorded")
            }

            override fun onAdLoaded() {
                Log.d(TAG, "✅ Banner ad loaded successfully!")
                Log.d(TAG, "✅ Ad unit ID: $adUnitId")
                Log.d(TAG, "✅ Ad should now be visible")
            }

            override fun onAdOpened() {
                Log.d(TAG, "🎯 Banner ad opened")
            }
        }

        try {
            adView.loadAd(adRequest)
            Log.d(TAG, "🔄 Loading banner ad with unit ID: $adUnitId")
            Log.d(TAG, "🔄 Ad request sent to AdMob...")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Exception loading banner ad: ${e.message}")
            e.printStackTrace()
        }
    }

    fun pauseAd(adView: AdView?) {
        try {
            adView?.pause()
        } catch (e: Exception) {
            Log.e(TAG, "Error pausing ad: ${e.message}")
        }
    }

    fun resumeAd(adView: AdView?) {
        try {
            adView?.resume()
        } catch (e: Exception) {
            Log.e(TAG, "Error resuming ad: ${e.message}")
        }
    }

    fun destroyAd(adView: AdView?) {
        try {
            adView?.destroy()
        } catch (e: Exception) {
            Log.e(TAG, "Error destroying ad: ${e.message}")
        }
    }
}
