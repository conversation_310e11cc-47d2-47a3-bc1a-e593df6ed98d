// Complete German Dictionary with 400 words (20 words per category)
const dictionary = [
  // Food (20 words)
  { word: "Apfel", pronunciation: "[ˈapfəl]", article: "der", english: "Apple", category: "Food", example: "Ich esse einen Apfel.", exampleEnglish: "I eat an apple.", audio: "https://example.com/audio/apfel.mp3" },
  { word: "Brot", pronunciation: "[broːt]", article: "das", english: "Bread", category: "Food", example: "Ich kaufe Brot.", exampleEnglish: "I buy bread.", audio: "https://example.com/audio/brot.mp3" },
  { word: "Käse", pronunciation: "[ˈkɛːzə]", article: "der", english: "Cheese", category: "Food", example: "Ich mag Käse.", exampleEnglish: "I like cheese.", audio: "https://example.com/audio/kaese.mp3" },
  { word: "Milch", pronunciation: "[mɪlç]", article: "die", english: "Milk", category: "Food", example: "Ich trinke Milch.", exampleEnglish: "I drink milk.", audio: "https://example.com/audio/milch.mp3" },
  { word: "Fleisch", pronunciation: "[flaɪ̯ʃ]", article: "das", english: "Meat", category: "Food", example: "Wir essen Fleisch.", exampleEnglish: "We eat meat.", audio: "https://example.com/audio/fleisch.mp3" },
  { word: "Fisch", pronunciation: "[fɪʃ]", article: "der", english: "Fish", category: "Food", example: "Ich esse Fisch.", exampleEnglish: "I eat fish.", audio: "https://example.com/audio/fisch.mp3" },
  { word: "Huhn", pronunciation: "[huːn]", article: "das", english: "Chicken", category: "Food", example: "Das Huhn ist lecker.", exampleEnglish: "The chicken is delicious.", audio: "https://example.com/audio/huhn.mp3" },
  { word: "Ei", pronunciation: "[aɪ̯]", article: "das", english: "Egg", category: "Food", example: "Ich esse ein Ei.", exampleEnglish: "I eat an egg.", audio: "https://example.com/audio/ei.mp3" },
  { word: "Butter", pronunciation: "[ˈbʊtɐ]", article: "die", english: "Butter", category: "Food", example: "Ich brauche Butter.", exampleEnglish: "I need butter.", audio: "https://example.com/audio/butter.mp3" },
  { word: "Zucker", pronunciation: "[ˈt͡sʊkɐ]", article: "der", english: "Sugar", category: "Food", example: "Der Zucker ist süß.", exampleEnglish: "The sugar is sweet.", audio: "https://example.com/audio/zucker.mp3" },
  { word: "Salz", pronunciation: "[zalt͡s]", article: "das", english: "Salt", category: "Food", example: "Das Salz ist wichtig.", exampleEnglish: "The salt is important.", audio: "https://example.com/audio/salz.mp3" },
  { word: "Reis", pronunciation: "[ʁaɪ̯s]", article: "der", english: "Rice", category: "Food", example: "Ich koche Reis.", exampleEnglish: "I cook rice.", audio: "https://example.com/audio/reis.mp3" },
  { word: "Nudeln", pronunciation: "[ˈnuːdl̩n]", article: "die", english: "Noodles", category: "Food", example: "Die Nudeln sind heiß.", exampleEnglish: "The noodles are hot.", audio: "https://example.com/audio/nudeln.mp3" },
  { word: "Kartoffel", pronunciation: "[kaʁˈtɔfl̩]", article: "die", english: "Potato", category: "Food", example: "Die Kartoffel ist groß.", exampleEnglish: "The potato is big.", audio: "https://example.com/audio/kartoffel.mp3" },
  { word: "Tomate", pronunciation: "[toˈmaːtə]", article: "die", english: "Tomato", category: "Food", example: "Die Tomate ist rot.", exampleEnglish: "The tomato is red.", audio: "https://example.com/audio/tomate.mp3" },
  { word: "Zwiebel", pronunciation: "[ˈt͡sviːbl̩]", article: "die", english: "Onion", category: "Food", example: "Die Zwiebel ist scharf.", exampleEnglish: "The onion is sharp.", audio: "https://example.com/audio/zwiebel.mp3" },
  { word: "Karotte", pronunciation: "[kaˈʁɔtə]", article: "die", english: "Carrot", category: "Food", example: "Die Karotte ist orange.", exampleEnglish: "The carrot is orange.", audio: "https://example.com/audio/karotte.mp3" },
  { word: "Salat", pronunciation: "[zaˈlaːt]", article: "der", english: "Salad", category: "Food", example: "Der Salat ist frisch.", exampleEnglish: "The salad is fresh.", audio: "https://example.com/audio/salat.mp3" },
  { word: "Suppe", pronunciation: "[ˈzʊpə]", article: "die", english: "Soup", category: "Food", example: "Die Suppe ist warm.", exampleEnglish: "The soup is warm.", audio: "https://example.com/audio/suppe.mp3" },
  { word: "Pizza", pronunciation: "[ˈpɪt͡sa]", article: "die", english: "Pizza", category: "Food", example: "Die Pizza schmeckt gut.", exampleEnglish: "The pizza tastes good.", audio: "https://example.com/audio/pizza.mp3" },

  // Drinks (20 words)
  { word: "Wasser", pronunciation: "[ˈvasɐ]", article: "das", english: "Water", category: "Drinks", example: "Ich trinke Wasser.", exampleEnglish: "I drink water.", audio: "https://example.com/audio/wasser.mp3" },
  { word: "Bier", pronunciation: "[biːɐ̯]", article: "das", english: "Beer", category: "Drinks", example: "Er trinkt ein Bier.", exampleEnglish: "He drinks a beer.", audio: "https://example.com/audio/bier.mp3" },
  { word: "Wein", pronunciation: "[vaɪ̯n]", article: "der", english: "Wine", category: "Drinks", example: "Sie mag Wein.", exampleEnglish: "She likes wine.", audio: "https://example.com/audio/wein.mp3" },
  { word: "Kaffee", pronunciation: "[ˈkafə]", article: "der", english: "Coffee", category: "Drinks", example: "Ich trinke Kaffee.", exampleEnglish: "I drink coffee.", audio: "https://example.com/audio/kaffee.mp3" },
  { word: "Tee", pronunciation: "[teː]", article: "der", english: "Tea", category: "Drinks", example: "Tee ist heiß.", exampleEnglish: "Tea is hot.", audio: "https://example.com/audio/tee.mp3" },
  { word: "Saft", pronunciation: "[zaft]", article: "der", english: "Juice", category: "Drinks", example: "Der Saft ist süß.", exampleEnglish: "The juice is sweet.", audio: "https://example.com/audio/saft.mp3" },
  { word: "Limonade", pronunciation: "[limoˈnaːdə]", article: "die", english: "Lemonade", category: "Drinks", example: "Die Limonade ist kalt.", exampleEnglish: "The lemonade is cold.", audio: "https://example.com/audio/limonade.mp3" },
  { word: "Cola", pronunciation: "[ˈkoːla]", article: "die", english: "Cola", category: "Drinks", example: "Ich trinke Cola.", exampleEnglish: "I drink cola.", audio: "https://example.com/audio/cola.mp3" },
  { word: "Milchshake", pronunciation: "[ˈmɪlçʃeɪk]", article: "der", english: "Milkshake", category: "Drinks", example: "Der Milchshake ist lecker.", exampleEnglish: "The milkshake is delicious.", audio: "https://example.com/audio/milchshake.mp3" },
  { word: "Smoothie", pronunciation: "[ˈsmuːði]", article: "der", english: "Smoothie", category: "Drinks", example: "Der Smoothie ist gesund.", exampleEnglish: "The smoothie is healthy.", audio: "https://example.com/audio/smoothie.mp3" },
  { word: "Mineralwasser", pronunciation: "[mineˈʁaːlvasɐ]", article: "das", english: "Mineral water", category: "Drinks", example: "Das Mineralwasser ist sprudelnd.", exampleEnglish: "The mineral water is sparkling.", audio: "https://example.com/audio/mineralwasser.mp3" },
  { word: "Orangensaft", pronunciation: "[oˈʁaŋənzaft]", article: "der", english: "Orange juice", category: "Drinks", example: "Der Orangensaft ist frisch.", exampleEnglish: "The orange juice is fresh.", audio: "https://example.com/audio/orangensaft.mp3" },
  { word: "Apfelsaft", pronunciation: "[ˈapfl̩zaft]", article: "der", english: "Apple juice", category: "Drinks", example: "Ich mag Apfelsaft.", exampleEnglish: "I like apple juice.", audio: "https://example.com/audio/apfelsaft.mp3" },
  { word: "Schokolade", pronunciation: "[ʃokoˈlaːdə]", article: "die", english: "Hot chocolate", category: "Drinks", example: "Die heiße Schokolade ist süß.", exampleEnglish: "The hot chocolate is sweet.", audio: "https://example.com/audio/schokolade.mp3" },
  { word: "Eiswasser", pronunciation: "[ˈaɪ̯svasɐ]", article: "das", english: "Ice water", category: "Drinks", example: "Das Eiswasser ist kalt.", exampleEnglish: "The ice water is cold.", audio: "https://example.com/audio/eiswasser.mp3" },
  { word: "Sprudel", pronunciation: "[ˈʃpʁuːdl̩]", article: "der", english: "Sparkling water", category: "Drinks", example: "Der Sprudel ist erfrischend.", exampleEnglish: "The sparkling water is refreshing.", audio: "https://example.com/audio/sprudel.mp3" },
  { word: "Cocktail", pronunciation: "[ˈkɔkteɪl]", article: "der", english: "Cocktail", category: "Drinks", example: "Der Cocktail ist bunt.", exampleEnglish: "The cocktail is colorful.", audio: "https://example.com/audio/cocktail.mp3" },
  { word: "Rum", pronunciation: "[ʁʊm]", article: "der", english: "Rum", category: "Drinks", example: "Der Rum ist stark.", exampleEnglish: "The rum is strong.", audio: "https://example.com/audio/rum.mp3" },
  { word: "Whisky", pronunciation: "[ˈvɪski]", article: "der", english: "Whisky", category: "Drinks", example: "Der Whisky ist alt.", exampleEnglish: "The whisky is old.", audio: "https://example.com/audio/whisky.mp3" },
  { word: "Champagner", pronunciation: "[ʃamˈpanjɐ]", article: "der", english: "Champagne", category: "Drinks", example: "Der Champagner ist teuer.", exampleEnglish: "The champagne is expensive.", audio: "https://example.com/audio/champagner.mp3" },

  // Greetings (20 words)
  { word: "Hallo", pronunciation: "[ˈhalo]", article: "", english: "Hello", category: "Greetings", example: "Hallo, wie geht's?", exampleEnglish: "Hello, how are you?", audio: "https://example.com/audio/hallo.mp3" },
  { word: "Danke", pronunciation: "[ˈdaŋkə]", article: "", english: "Thank you", category: "Greetings", example: "Danke für die Hilfe.", exampleEnglish: "Thank you for the help.", audio: "https://example.com/audio/danke.mp3" },
  { word: "Bitte", pronunciation: "[ˈbɪtə]", article: "", english: "Please", category: "Greetings", example: "Bitte, komm herein.", exampleEnglish: "Please, come in.", audio: "https://example.com/audio/bitte.mp3" },
  { word: "Tschüss", pronunciation: "[tʃʏs]", article: "", english: "Bye", category: "Greetings", example: "Tschüss, bis morgen!", exampleEnglish: "Bye, see you tomorrow!", audio: "https://example.com/audio/tschuess.mp3" },
  { word: "Guten Morgen", pronunciation: "[ˈɡuːtən ˈmɔʁɡən]", article: "", english: "Good morning", category: "Greetings", example: "Guten Morgen, wie geht's?", exampleEnglish: "Good morning, how are you?", audio: "https://example.com/audio/gutenmorgen.mp3" },
  { word: "Guten Tag", pronunciation: "[ˈɡuːtən taːk]", article: "", english: "Good day", category: "Greetings", example: "Guten Tag, Herr Schmidt.", exampleEnglish: "Good day, Mr. Schmidt.", audio: "https://example.com/audio/gutentag.mp3" },
  { word: "Guten Abend", pronunciation: "[ˈɡuːtən ˈaːbənt]", article: "", english: "Good evening", category: "Greetings", example: "Guten Abend, alle zusammen.", exampleEnglish: "Good evening, everyone.", audio: "https://example.com/audio/gutenabend.mp3" },
  { word: "Gute Nacht", pronunciation: "[ˈɡuːtə naxt]", article: "", english: "Good night", category: "Greetings", example: "Gute Nacht, schlaf gut.", exampleEnglish: "Good night, sleep well.", audio: "https://example.com/audio/gutenacht.mp3" },
  { word: "Entschuldigung", pronunciation: "[ɛntˈʃʊldɪɡʊŋ]", article: "", english: "Excuse me", category: "Greetings", example: "Entschuldigung, wo ist der Bahnhof?", exampleEnglish: "Excuse me, where is the train station?", audio: "https://example.com/audio/entschuldigung.mp3" },
  { word: "Verzeihung", pronunciation: "[fɛɐ̯ˈt͡saɪ̯ʊŋ]", article: "", english: "Pardon", category: "Greetings", example: "Verzeihung, das war mein Fehler.", exampleEnglish: "Pardon, that was my mistake.", audio: "https://example.com/audio/verzeihung.mp3" },
  { word: "Willkommen", pronunciation: "[vɪlˈkɔmən]", article: "", english: "Welcome", category: "Greetings", example: "Willkommen in Deutschland!", exampleEnglish: "Welcome to Germany!", audio: "https://example.com/audio/willkommen.mp3" },
  { word: "Auf Wiedersehen", pronunciation: "[aʊ̯f ˈviːdɐzeːən]", article: "", english: "Goodbye", category: "Greetings", example: "Auf Wiedersehen, bis bald!", exampleEnglish: "Goodbye, see you soon!", audio: "https://example.com/audio/aufwiedersehen.mp3" },
  { word: "Bis später", pronunciation: "[bɪs ˈʃpɛːtɐ]", article: "", english: "See you later", category: "Greetings", example: "Bis später, ich muss gehen.", exampleEnglish: "See you later, I have to go.", audio: "https://example.com/audio/bisspaeter.mp3" },
  { word: "Wie geht's", pronunciation: "[viː ɡeːts]", article: "", english: "How are you", category: "Greetings", example: "Hallo, wie geht's dir?", exampleEnglish: "Hello, how are you?", audio: "https://example.com/audio/wiegehts.mp3" },
  { word: "Gut", pronunciation: "[ɡuːt]", article: "", english: "Good", category: "Greetings", example: "Mir geht es gut.", exampleEnglish: "I am doing well.", audio: "https://example.com/audio/gut.mp3" },
  { word: "Schlecht", pronunciation: "[ʃlɛçt]", article: "", english: "Bad", category: "Greetings", example: "Mir geht es schlecht.", exampleEnglish: "I am doing badly.", audio: "https://example.com/audio/schlecht.mp3" },
  { word: "Freut mich", pronunciation: "[fʁɔɪ̯t mɪç]", article: "", english: "Nice to meet you", category: "Greetings", example: "Freut mich, Sie kennenzulernen.", exampleEnglish: "Nice to meet you.", audio: "https://example.com/audio/freutmich.mp3" },
  { word: "Prost", pronunciation: "[pʁoːst]", article: "", english: "Cheers", category: "Greetings", example: "Prost! Auf die Freundschaft!", exampleEnglish: "Cheers! To friendship!", audio: "https://example.com/audio/prost.mp3" },
  { word: "Gesundheit", pronunciation: "[ɡəˈzʊnthaɪ̯t]", article: "", english: "Bless you", category: "Greetings", example: "Gesundheit! Bist du erkältet?", exampleEnglish: "Bless you! Do you have a cold?", audio: "https://example.com/audio/gesundheit.mp3" },
  { word: "Alles Gute", pronunciation: "[ˈaləs ˈɡuːtə]", article: "", english: "All the best", category: "Greetings", example: "Alles Gute zum Geburtstag!", exampleEnglish: "Happy birthday!", audio: "https://example.com/audio/allesgute.mp3" },

  // Numbers (20 words)
  { word: "Null", pronunciation: "[nʊl]", article: "", english: "Zero", category: "Numbers", example: "Die Zahl ist null.", audio: "https://example.com/audio/null.mp3" },
  { word: "Eins", pronunciation: "[aɪ̯ns]", article: "", english: "One", category: "Numbers", example: "Ich habe eins Buch.", audio: "https://example.com/audio/eins.mp3" },
  { word: "Zwei", pronunciation: "[tsvaɪ̯]", article: "", english: "Two", category: "Numbers", example: "Zwei Äpfel, bitte.", audio: "https://example.com/audio/zwei.mp3" },
  { word: "Drei", pronunciation: "[dʁaɪ̯]", article: "", english: "Three", category: "Numbers", example: "Drei Katzen laufen.", audio: "https://example.com/audio/drei.mp3" },
  { word: "Vier", pronunciation: "[fiːɐ̯]", article: "", english: "Four", category: "Numbers", example: "Vier Stühle stehen hier.", audio: "https://example.com/audio/vier.mp3" },
  { word: "Fünf", pronunciation: "[fʏnf]", article: "", english: "Five", category: "Numbers", example: "Fünf Bücher sind neu.", audio: "https://example.com/audio/fuenf.mp3" },
  { word: "Sechs", pronunciation: "[zɛks]", article: "", english: "Six", category: "Numbers", example: "Sechs Tage die Woche.", audio: "https://example.com/audio/sechs.mp3" },
  { word: "Sieben", pronunciation: "[ˈziːbən]", article: "", english: "Seven", category: "Numbers", example: "Sieben Farben im Regenbogen.", audio: "https://example.com/audio/sieben.mp3" },
  { word: "Acht", pronunciation: "[axt]", article: "", english: "Eight", category: "Numbers", example: "Acht Uhr morgens.", audio: "https://example.com/audio/acht.mp3" },
  { word: "Neun", pronunciation: "[nɔɪ̯n]", article: "", english: "Nine", category: "Numbers", example: "Neun Leben hat eine Katze.", audio: "https://example.com/audio/neun.mp3" },
  { word: "Zehn", pronunciation: "[t͡seːn]", article: "", english: "Ten", category: "Numbers", example: "Zehn Finger habe ich.", audio: "https://example.com/audio/zehn.mp3" },
  { word: "Elf", pronunciation: "[ɛlf]", article: "", english: "Eleven", category: "Numbers", example: "Elf Spieler im Team.", audio: "https://example.com/audio/elf.mp3" },
  { word: "Zwölf", pronunciation: "[t͡svœlf]", article: "", english: "Twelve", category: "Numbers", example: "Zwölf Monate im Jahr.", audio: "https://example.com/audio/zwoelf.mp3" },
  { word: "Dreizehn", pronunciation: "[ˈdʁaɪ̯t͡seːn]", article: "", english: "Thirteen", category: "Numbers", example: "Dreizehn ist eine Unglückszahl.", audio: "https://example.com/audio/dreizehn.mp3" },
  { word: "Vierzehn", pronunciation: "[ˈfiːɐ̯t͡seːn]", article: "", english: "Fourteen", category: "Numbers", example: "Vierzehn Tage sind zwei Wochen.", audio: "https://example.com/audio/vierzehn.mp3" },
  { word: "Fünfzehn", pronunciation: "[ˈfʏnft͡seːn]", article: "", english: "Fifteen", category: "Numbers", example: "Fünfzehn Minuten Pause.", audio: "https://example.com/audio/fuenfzehn.mp3" },
  { word: "Zwanzig", pronunciation: "[ˈt͡svant͡sɪç]", article: "", english: "Twenty", category: "Numbers", example: "Zwanzig Euro kostet das.", audio: "https://example.com/audio/zwanzig.mp3" },
  { word: "Dreißig", pronunciation: "[ˈdʁaɪ̯sɪç]", article: "", english: "Thirty", category: "Numbers", example: "Dreißig Grad ist heiß.", audio: "https://example.com/audio/dreissig.mp3" },
  { word: "Hundert", pronunciation: "[ˈhʊndɐt]", article: "", english: "Hundred", category: "Numbers", example: "Hundert Menschen waren da.", audio: "https://example.com/audio/hundert.mp3" },
  { word: "Tausend", pronunciation: "[ˈtaʊ̯zənt]", article: "", english: "Thousand", category: "Numbers", example: "Tausend Sterne am Himmel.", audio: "https://example.com/audio/tausend.mp3" },

  // Colors (20 words)
  { word: "Rot", pronunciation: "[ʁoːt]", article: "", english: "Red", category: "Colors", example: "Das Auto ist rot.", audio: "https://example.com/audio/rot.mp3" },
  { word: "Blau", pronunciation: "[blaʊ̯]", article: "", english: "Blue", category: "Colors", example: "Der Himmel ist blau.", audio: "https://example.com/audio/blau.mp3" },
  { word: "Grün", pronunciation: "[ɡʁyːn]", article: "", english: "Green", category: "Colors", example: "Das Gras ist grün.", audio: "https://example.com/audio/gruen.mp3" },
  { word: "Gelb", pronunciation: "[ɡɛlp]", article: "", english: "Yellow", category: "Colors", example: "Die Sonne ist gelb.", audio: "https://example.com/audio/gelb.mp3" },
  { word: "Weiß", pronunciation: "[vaɪ̯s]", article: "", english: "White", category: "Colors", example: "Der Schnee ist weiß.", audio: "https://example.com/audio/weiss.mp3" },
  { word: "Schwarz", pronunciation: "[ʃvaʁt͡s]", article: "", english: "Black", category: "Colors", example: "Die Nacht ist schwarz.", audio: "https://example.com/audio/schwarz.mp3" },
  { word: "Grau", pronunciation: "[ɡʁaʊ̯]", article: "", english: "Gray", category: "Colors", example: "Die Wolke ist grau.", audio: "https://example.com/audio/grau.mp3" },
  { word: "Rosa", pronunciation: "[ˈʁoːza]", article: "", english: "Pink", category: "Colors", example: "Die Blume ist rosa.", audio: "https://example.com/audio/rosa.mp3" },
  { word: "Lila", pronunciation: "[ˈliːla]", article: "", english: "Purple", category: "Colors", example: "Das Kleid ist lila.", audio: "https://example.com/audio/lila.mp3" },
  { word: "Orange", pronunciation: "[oˈʁaŋʒə]", article: "", english: "Orange", category: "Colors", example: "Die Orange ist orange.", audio: "https://example.com/audio/orange.mp3" },
  { word: "Braun", pronunciation: "[bʁaʊ̯n]", article: "", english: "Brown", category: "Colors", example: "Der Baum ist braun.", audio: "https://example.com/audio/braun.mp3" },
  { word: "Violett", pronunciation: "[vioˈlɛt]", article: "", english: "Violet", category: "Colors", example: "Die Blüte ist violett.", audio: "https://example.com/audio/violett.mp3" },
  { word: "Türkis", pronunciation: "[tʏʁˈkiːs]", article: "", english: "Turquoise", category: "Colors", example: "Das Wasser ist türkis.", audio: "https://example.com/audio/tuerkis.mp3" },
  { word: "Gold", pronunciation: "[ɡɔlt]", article: "", english: "Gold", category: "Colors", example: "Der Ring ist gold.", audio: "https://example.com/audio/gold.mp3" },
  { word: "Silber", pronunciation: "[ˈzɪlbɐ]", article: "", english: "Silver", category: "Colors", example: "Die Münze ist silber.", audio: "https://example.com/audio/silber.mp3" },
  { word: "Beige", pronunciation: "[beːʒ]", article: "", english: "Beige", category: "Colors", example: "Die Wand ist beige.", audio: "https://example.com/audio/beige.mp3" },
  { word: "Magenta", pronunciation: "[maˈɡɛnta]", article: "", english: "Magenta", category: "Colors", example: "Die Farbe ist magenta.", audio: "https://example.com/audio/magenta.mp3" },
  { word: "Cyan", pronunciation: "[t͡syˈaːn]", article: "", english: "Cyan", category: "Colors", example: "Der Himmel ist cyan.", audio: "https://example.com/audio/cyan.mp3" },
  { word: "Khaki", pronunciation: "[ˈkaːki]", article: "", english: "Khaki", category: "Colors", example: "Die Hose ist khaki.", audio: "https://example.com/audio/khaki.mp3" },
  { word: "Bordeaux", pronunciation: "[bɔʁˈdoː]", article: "", english: "Bordeaux", category: "Colors", example: "Der Wein ist bordeaux.", audio: "https://example.com/audio/bordeaux.mp3" },

  // Animals (20 words)
  { word: "Hund", pronunciation: "[hʊnt]", article: "der", english: "Dog", category: "Animals", example: "Der Hund bellt.", audio: "https://example.com/audio/hund.mp3" },
  { word: "Katze", pronunciation: "[ˈkat͡sə]", article: "die", english: "Cat", category: "Animals", example: "Die Katze schläft.", audio: "https://example.com/audio/katze.mp3" },
  { word: "Vogel", pronunciation: "[ˈfoːɡl̩]", article: "der", english: "Bird", category: "Animals", example: "Der Vogel singt.", audio: "https://example.com/audio/vogel.mp3" },
  { word: "Pferd", pronunciation: "[pfɛʁt]", article: "das", english: "Horse", category: "Animals", example: "Das Pferd galoppiert.", audio: "https://example.com/audio/pferd.mp3" },
  { word: "Fisch", pronunciation: "[fɪʃ]", article: "der", english: "Fish", category: "Animals", example: "Der Fisch schwimmt.", audio: "https://example.com/audio/fisch.mp3" },
  { word: "Kuh", pronunciation: "[kuː]", article: "die", english: "Cow", category: "Animals", example: "Die Kuh gibt Milch.", audio: "https://example.com/audio/kuh.mp3" },
  { word: "Schwein", pronunciation: "[ʃvaɪ̯n]", article: "das", english: "Pig", category: "Animals", example: "Das Schwein ist rosa.", audio: "https://example.com/audio/schwein.mp3" },
  { word: "Schaf", pronunciation: "[ʃaːf]", article: "das", english: "Sheep", category: "Animals", example: "Das Schaf ist weiß.", audio: "https://example.com/audio/schaf.mp3" },
  { word: "Ziege", pronunciation: "[ˈt͡siːɡə]", article: "die", english: "Goat", category: "Animals", example: "Die Ziege klettert.", audio: "https://example.com/audio/ziege.mp3" },
  { word: "Hase", pronunciation: "[ˈhaːzə]", article: "der", english: "Rabbit", category: "Animals", example: "Der Hase hüpft.", audio: "https://example.com/audio/hase.mp3" },
  { word: "Maus", pronunciation: "[maʊ̯s]", article: "die", english: "Mouse", category: "Animals", example: "Die Maus ist klein.", audio: "https://example.com/audio/maus.mp3" },
  { word: "Elefant", pronunciation: "[eleˈfant]", article: "der", english: "Elephant", category: "Animals", example: "Der Elefant ist groß.", audio: "https://example.com/audio/elefant.mp3" },
  { word: "Löwe", pronunciation: "[ˈløːvə]", article: "der", english: "Lion", category: "Animals", example: "Der Löwe brüllt.", audio: "https://example.com/audio/loewe.mp3" },
  { word: "Tiger", pronunciation: "[ˈtiːɡɐ]", article: "der", english: "Tiger", category: "Animals", example: "Der Tiger ist gestreift.", audio: "https://example.com/audio/tiger.mp3" },
  { word: "Bär", pronunciation: "[bɛːɐ̯]", article: "der", english: "Bear", category: "Animals", example: "Der Bär ist braun.", audio: "https://example.com/audio/baer.mp3" },
  { word: "Wolf", pronunciation: "[vɔlf]", article: "der", english: "Wolf", category: "Animals", example: "Der Wolf heult.", audio: "https://example.com/audio/wolf.mp3" },
  { word: "Fuchs", pronunciation: "[fʊks]", article: "der", english: "Fox", category: "Animals", example: "Der Fuchs ist schlau.", audio: "https://example.com/audio/fuchs.mp3" },
  { word: "Eule", pronunciation: "[ˈɔɪ̯lə]", article: "die", english: "Owl", category: "Animals", example: "Die Eule ist weise.", audio: "https://example.com/audio/eule.mp3" },
  { word: "Adler", pronunciation: "[ˈaːdlɐ]", article: "der", english: "Eagle", category: "Animals", example: "Der Adler fliegt hoch.", audio: "https://example.com/audio/adler.mp3" },
  { word: "Schlange", pronunciation: "[ˈʃlaŋə]", article: "die", english: "Snake", category: "Animals", example: "Die Schlange kriecht.", audio: "https://example.com/audio/schlange.mp3" },

  // Family (20 words)
  { word: "Mutter", pronunciation: "[ˈmʊtɐ]", article: "die", english: "Mother", category: "Family", example: "Meine Mutter kocht.", audio: "https://example.com/audio/mutter.mp3" },
  { word: "Vater", pronunciation: "[ˈfaːtɐ]", article: "der", english: "Father", category: "Family", example: "Mein Vater arbeitet.", audio: "https://example.com/audio/vater.mp3" },
  { word: "Bruder", pronunciation: "[ˈbʁuːdɐ]", article: "der", english: "Brother", category: "Family", example: "Mein Bruder spielt.", audio: "https://example.com/audio/bruder.mp3" },
  { word: "Schwester", pronunciation: "[ˈʃvɛstɐ]", article: "die", english: "Sister", category: "Family", example: "Meine Schwester liest.", audio: "https://example.com/audio/schwester.mp3" },
  { word: "Oma", pronunciation: "[ˈoːma]", article: "die", english: "Grandmother", category: "Family", example: "Oma backt Kuchen.", audio: "https://example.com/audio/oma.mp3" },
  { word: "Opa", pronunciation: "[ˈoːpa]", article: "der", english: "Grandfather", category: "Family", example: "Opa erzählt Geschichten.", audio: "https://example.com/audio/opa.mp3" },
  { word: "Tante", pronunciation: "[ˈtantə]", article: "die", english: "Aunt", category: "Family", example: "Meine Tante besucht uns.", audio: "https://example.com/audio/tante.mp3" },
  { word: "Onkel", pronunciation: "[ˈɔŋkl̩]", article: "der", english: "Uncle", category: "Family", example: "Mein Onkel ist lustig.", audio: "https://example.com/audio/onkel.mp3" },
  { word: "Cousin", pronunciation: "[kuˈzɛ̃ː]", article: "der", english: "Cousin", category: "Family", example: "Mein Cousin ist älter.", audio: "https://example.com/audio/cousin.mp3" },
  { word: "Cousine", pronunciation: "[kuˈziːnə]", article: "die", english: "Female cousin", category: "Family", example: "Meine Cousine ist nett.", audio: "https://example.com/audio/cousine.mp3" },
  { word: "Sohn", pronunciation: "[zoːn]", article: "der", english: "Son", category: "Family", example: "Mein Sohn ist klein.", audio: "https://example.com/audio/sohn.mp3" },
  { word: "Tochter", pronunciation: "[ˈtɔxtɐ]", article: "die", english: "Daughter", category: "Family", example: "Meine Tochter singt.", audio: "https://example.com/audio/tochter.mp3" },
  { word: "Ehemann", pronunciation: "[ˈeːəman]", article: "der", english: "Husband", category: "Family", example: "Mein Ehemann ist lieb.", audio: "https://example.com/audio/ehemann.mp3" },
  { word: "Ehefrau", pronunciation: "[ˈeːəfʁaʊ̯]", article: "die", english: "Wife", category: "Family", example: "Meine Ehefrau ist schön.", audio: "https://example.com/audio/ehefrau.mp3" },
  { word: "Kind", pronunciation: "[kɪnt]", article: "das", english: "Child", category: "Family", example: "Das Kind spielt.", audio: "https://example.com/audio/kind.mp3" },
  { word: "Baby", pronunciation: "[ˈbeːbi]", article: "das", english: "Baby", category: "Family", example: "Das Baby schläft.", audio: "https://example.com/audio/baby.mp3" },
  { word: "Eltern", pronunciation: "[ˈɛltɐn]", article: "die", english: "Parents", category: "Family", example: "Meine Eltern sind nett.", audio: "https://example.com/audio/eltern.mp3" },
  { word: "Großeltern", pronunciation: "[ˈɡʁoːsɛltɐn]", article: "die", english: "Grandparents", category: "Family", example: "Die Großeltern sind alt.", audio: "https://example.com/audio/grosseltern.mp3" },
  { word: "Enkel", pronunciation: "[ˈɛŋkl̩]", article: "der", english: "Grandson", category: "Family", example: "Der Enkel ist süß.", audio: "https://example.com/audio/enkel.mp3" },
  { word: "Enkelin", pronunciation: "[ˈɛŋkəlɪn]", article: "die", english: "Granddaughter", category: "Family", example: "Die Enkelin ist klug.", audio: "https://example.com/audio/enkelin.mp3" },

  // Time (20 words)
  { word: "Heute", pronunciation: "[ˈhɔɪ̯tə]", article: "", english: "Today", category: "Time", example: "Heute ist Montag.", audio: "https://example.com/audio/heute.mp3" },
  { word: "Morgen", pronunciation: "[ˈmɔʁɡən]", article: "", english: "Tomorrow", category: "Time", example: "Morgen gehe ich.", audio: "https://example.com/audio/morgen.mp3" },
  { word: "Gestern", pronunciation: "[ˈɡɛstɐn]", article: "", english: "Yesterday", category: "Time", example: "Gestern war Sonntag.", audio: "https://example.com/audio/gestern.mp3" },
  { word: "Uhr", pronunciation: "[uːɐ̯]", article: "die", english: "Clock/Time", category: "Time", example: "Es ist acht Uhr.", audio: "https://example.com/audio/uhr.mp3" },
  { word: "Woche", pronunciation: "[ˈvɔxə]", article: "die", english: "Week", category: "Time", example: "Die Woche beginnt.", audio: "https://example.com/audio/woche.mp3" },
  { word: "Monat", pronunciation: "[ˈmoːnat]", article: "der", english: "Month", category: "Time", example: "Der Monat ist lang.", audio: "https://example.com/audio/monat.mp3" },
  { word: "Jahr", pronunciation: "[jaːɐ̯]", article: "das", english: "Year", category: "Time", example: "Das Jahr ist neu.", audio: "https://example.com/audio/jahr.mp3" },
  { word: "Tag", pronunciation: "[taːk]", article: "der", english: "Day", category: "Time", example: "Der Tag ist schön.", audio: "https://example.com/audio/tag.mp3" },
  { word: "Nacht", pronunciation: "[naxt]", article: "die", english: "Night", category: "Time", example: "Die Nacht ist dunkel.", audio: "https://example.com/audio/nacht.mp3" },
  { word: "Stunde", pronunciation: "[ˈʃtʊndə]", article: "die", english: "Hour", category: "Time", example: "Eine Stunde ist lang.", audio: "https://example.com/audio/stunde.mp3" },
  { word: "Minute", pronunciation: "[miˈnuːtə]", article: "die", english: "Minute", category: "Time", example: "Eine Minute ist kurz.", audio: "https://example.com/audio/minute.mp3" },
  { word: "Sekunde", pronunciation: "[zeˈkʊndə]", article: "die", english: "Second", category: "Time", example: "Eine Sekunde vergeht.", audio: "https://example.com/audio/sekunde.mp3" },
  { word: "Morgen", pronunciation: "[ˈmɔʁɡən]", article: "der", english: "Morning", category: "Time", example: "Der Morgen ist früh.", audio: "https://example.com/audio/morgen2.mp3" },
  { word: "Mittag", pronunciation: "[ˈmɪtaːk]", article: "der", english: "Noon", category: "Time", example: "Der Mittag ist heiß.", audio: "https://example.com/audio/mittag.mp3" },
  { word: "Abend", pronunciation: "[ˈaːbənt]", article: "der", english: "Evening", category: "Time", example: "Der Abend ist ruhig.", audio: "https://example.com/audio/abend.mp3" },
  { word: "Wochenende", pronunciation: "[ˈvɔxənɛndə]", article: "das", english: "Weekend", category: "Time", example: "Das Wochenende ist frei.", audio: "https://example.com/audio/wochenende.mp3" },
  { word: "Montag", pronunciation: "[ˈmoːntaːk]", article: "der", english: "Monday", category: "Time", example: "Montag ist Arbeitstag.", audio: "https://example.com/audio/montag.mp3" },
  { word: "Dienstag", pronunciation: "[ˈdiːnstaːk]", article: "der", english: "Tuesday", category: "Time", example: "Dienstag kommt nach Montag.", audio: "https://example.com/audio/dienstag.mp3" },
  { word: "Mittwoch", pronunciation: "[ˈmɪtvɔx]", article: "der", english: "Wednesday", category: "Time", example: "Mittwoch ist Wochenmitte.", audio: "https://example.com/audio/mittwoch.mp3" },
  { word: "Donnerstag", pronunciation: "[ˈdɔnɐstaːk]", article: "der", english: "Thursday", category: "Time", example: "Donnerstag ist fast Wochenende.", audio: "https://example.com/audio/donnerstag.mp3" },

  // Weather (20 words)
  { word: "Sonne", pronunciation: "[ˈzɔnə]", article: "die", english: "Sun", category: "Weather", example: "Die Sonne scheint.", audio: "https://example.com/audio/sonne.mp3" },
  { word: "Regen", pronunciation: "[ˈʁeːɡən]", article: "der", english: "Rain", category: "Weather", example: "Es gibt Regen.", audio: "https://example.com/audio/regen.mp3" },
  { word: "Schnee", pronunciation: "[ʃneː]", article: "der", english: "Snow", category: "Weather", example: "Der Schnee fällt.", audio: "https://example.com/audio/schnee.mp3" },
  { word: "Wind", pronunciation: "[vɪnt]", article: "der", english: "Wind", category: "Weather", example: "Der Wind weht.", audio: "https://example.com/audio/wind.mp3" },
  { word: "Wolke", pronunciation: "[ˈvɔlkə]", article: "die", english: "Cloud", category: "Weather", example: "Die Wolke ist grau.", audio: "https://example.com/audio/wolke.mp3" },
  { word: "Sturm", pronunciation: "[ʃtʊʁm]", article: "der", english: "Storm", category: "Weather", example: "Der Sturm ist stark.", audio: "https://example.com/audio/sturm.mp3" },
  { word: "Gewitter", pronunciation: "[ɡəˈvɪtɐ]", article: "das", english: "Thunderstorm", category: "Weather", example: "Das Gewitter ist laut.", audio: "https://example.com/audio/gewitter.mp3" },
  { word: "Blitz", pronunciation: "[blɪt͡s]", article: "der", english: "Lightning", category: "Weather", example: "Der Blitz ist hell.", audio: "https://example.com/audio/blitz.mp3" },
  { word: "Donner", pronunciation: "[ˈdɔnɐ]", article: "der", english: "Thunder", category: "Weather", example: "Der Donner ist laut.", audio: "https://example.com/audio/donner.mp3" },
  { word: "Hagel", pronunciation: "[ˈhaːɡl̩]", article: "der", english: "Hail", category: "Weather", example: "Der Hagel fällt.", audio: "https://example.com/audio/hagel.mp3" },
  { word: "Nebel", pronunciation: "[ˈneːbl̩]", article: "der", english: "Fog", category: "Weather", example: "Der Nebel ist dicht.", audio: "https://example.com/audio/nebel.mp3" },
  { word: "Frost", pronunciation: "[fʁɔst]", article: "der", english: "Frost", category: "Weather", example: "Der Frost ist kalt.", audio: "https://example.com/audio/frost.mp3" },
  { word: "Eis", pronunciation: "[aɪ̯s]", article: "das", english: "Ice", category: "Weather", example: "Das Eis ist glatt.", audio: "https://example.com/audio/eis.mp3" },
  { word: "Hitze", pronunciation: "[ˈhɪt͡sə]", article: "die", english: "Heat", category: "Weather", example: "Die Hitze ist stark.", audio: "https://example.com/audio/hitze.mp3" },
  { word: "Kälte", pronunciation: "[ˈkɛltə]", article: "die", english: "Cold", category: "Weather", example: "Die Kälte ist bitter.", audio: "https://example.com/audio/kaelte.mp3" },
  { word: "Temperatur", pronunciation: "[tɛmpəʁaˈtuːɐ̯]", article: "die", english: "Temperature", category: "Weather", example: "Die Temperatur steigt.", audio: "https://example.com/audio/temperatur.mp3" },
  { word: "Himmel", pronunciation: "[ˈhɪml̩]", article: "der", english: "Sky", category: "Weather", example: "Der Himmel ist blau.", audio: "https://example.com/audio/himmel.mp3" },
  { word: "Regenbogen", pronunciation: "[ˈʁeːɡənboːɡən]", article: "der", english: "Rainbow", category: "Weather", example: "Der Regenbogen ist bunt.", audio: "https://example.com/audio/regenbogen.mp3" },
  { word: "Schatten", pronunciation: "[ˈʃatən]", article: "der", english: "Shadow", category: "Weather", example: "Der Schatten ist kühl.", audio: "https://example.com/audio/schatten.mp3" },
  { word: "Sonnenschein", pronunciation: "[ˈzɔnənʃaɪ̯n]", article: "der", english: "Sunshine", category: "Weather", example: "Der Sonnenschein ist warm.", audio: "https://example.com/audio/sonnenschein.mp3" },

  // Clothing (20 words)
  { word: "Hose", pronunciation: "[ˈhoːzə]", article: "die", english: "Pants", category: "Clothing", example: "Ich trage eine Hose.", audio: "https://example.com/audio/hose.mp3" },
  { word: "Hemd", pronunciation: "[hɛmt]", article: "das", english: "Shirt", category: "Clothing", example: "Das Hemd ist weiß.", audio: "https://example.com/audio/hemd.mp3" },
  { word: "Schuhe", pronunciation: "[ˈʃuːə]", article: "die", english: "Shoes", category: "Clothing", example: "Meine Schuhe sind neu.", audio: "https://example.com/audio/schuhe.mp3" },
  { word: "Jacke", pronunciation: "[ˈjakə]", article: "die", english: "Jacket", category: "Clothing", example: "Die Jacke ist warm.", audio: "https://example.com/audio/jacke.mp3" },
  { word: "Hut", pronunciation: "[huːt]", article: "der", english: "Hat", category: "Clothing", example: "Er trägt einen Hut.", audio: "https://example.com/audio/hut.mp3" },
  { word: "Kleid", pronunciation: "[klaɪ̯t]", article: "das", english: "Dress", category: "Clothing", example: "Das Kleid ist schön.", audio: "https://example.com/audio/kleid.mp3" },
  { word: "Rock", pronunciation: "[ʁɔk]", article: "der", english: "Skirt", category: "Clothing", example: "Der Rock ist kurz.", audio: "https://example.com/audio/rock.mp3" },
  { word: "Pullover", pronunciation: "[pʊˈloːvɐ]", article: "der", english: "Sweater", category: "Clothing", example: "Der Pullover ist warm.", audio: "https://example.com/audio/pullover.mp3" },
  { word: "T-Shirt", pronunciation: "[ˈtiːʃɛʁt]", article: "das", english: "T-shirt", category: "Clothing", example: "Das T-Shirt ist bequem.", audio: "https://example.com/audio/tshirt.mp3" },
  { word: "Jeans", pronunciation: "[d͡ʒiːns]", article: "die", english: "Jeans", category: "Clothing", example: "Die Jeans ist blau.", audio: "https://example.com/audio/jeans.mp3" },
  { word: "Socken", pronunciation: "[ˈzɔkən]", article: "die", english: "Socks", category: "Clothing", example: "Die Socken sind warm.", audio: "https://example.com/audio/socken.mp3" },
  { word: "Unterwäsche", pronunciation: "[ˈʊntɐvɛʃə]", article: "die", english: "Underwear", category: "Clothing", example: "Die Unterwäsche ist sauber.", audio: "https://example.com/audio/unterwaesche.mp3" },
  { word: "Mantel", pronunciation: "[ˈmantl̩]", article: "der", english: "Coat", category: "Clothing", example: "Der Mantel ist lang.", audio: "https://example.com/audio/mantel.mp3" },
  { word: "Handschuhe", pronunciation: "[ˈhantʃuːə]", article: "die", english: "Gloves", category: "Clothing", example: "Die Handschuhe sind warm.", audio: "https://example.com/audio/handschuhe.mp3" },
  { word: "Schal", pronunciation: "[ʃaːl]", article: "der", english: "Scarf", category: "Clothing", example: "Der Schal ist lang.", audio: "https://example.com/audio/schal.mp3" },
  { word: "Mütze", pronunciation: "[ˈmʏt͡sə]", article: "die", english: "Cap", category: "Clothing", example: "Die Mütze ist warm.", audio: "https://example.com/audio/muetze.mp3" },
  { word: "Gürtel", pronunciation: "[ˈɡʏʁtl̩]", article: "der", english: "Belt", category: "Clothing", example: "Der Gürtel ist schwarz.", audio: "https://example.com/audio/guertel.mp3" },
  { word: "Krawatte", pronunciation: "[kʁaˈvatə]", article: "die", english: "Tie", category: "Clothing", example: "Die Krawatte ist elegant.", audio: "https://example.com/audio/krawatte.mp3" },
  { word: "Stiefel", pronunciation: "[ˈʃtiːfl̩]", article: "die", english: "Boots", category: "Clothing", example: "Die Stiefel sind hoch.", audio: "https://example.com/audio/stiefel.mp3" },
  { word: "Sandalen", pronunciation: "[zanˈdaːlən]", article: "die", english: "Sandals", category: "Clothing", example: "Die Sandalen sind bequem.", audio: "https://example.com/audio/sandalen.mp3" },

  // Objects (20 words)
  { word: "Buch", pronunciation: "[buːx]", article: "das", english: "Book", category: "Objects", example: "Ich lese ein Buch.", audio: "https://example.com/audio/buch.mp3" },
  { word: "Tisch", pronunciation: "[tɪʃ]", article: "der", english: "Table", category: "Objects", example: "Der Tisch ist groß.", audio: "https://example.com/audio/tisch.mp3" },
  { word: "Stuhl", pronunciation: "[ʃtuːl]", article: "der", english: "Chair", category: "Objects", example: "Der Stuhl ist bequem.", audio: "https://example.com/audio/stuhl.mp3" },
  { word: "Lampe", pronunciation: "[ˈlampə]", article: "die", english: "Lamp", category: "Objects", example: "Die Lampe leuchtet.", audio: "https://example.com/audio/lampe.mp3" },
  { word: "Stift", pronunciation: "[ʃtɪft]", article: "der", english: "Pen", category: "Objects", example: "Ich brauche einen Stift.", audio: "https://example.com/audio/stift.mp3" },
  { word: "Computer", pronunciation: "[kɔmˈpjuːtɐ]", article: "der", english: "Computer", category: "Objects", example: "Der Computer ist schnell.", audio: "https://example.com/audio/computer.mp3" },
  { word: "Telefon", pronunciation: "[teləˈfoːn]", article: "das", english: "Phone", category: "Objects", example: "Das Telefon klingelt.", audio: "https://example.com/audio/telefon.mp3" },
  { word: "Fernseher", pronunciation: "[ˈfɛʁnzeːɐ]", article: "der", english: "Television", category: "Objects", example: "Der Fernseher ist groß.", audio: "https://example.com/audio/fernseher.mp3" },
  { word: "Radio", pronunciation: "[ˈʁaːdio]", article: "das", english: "Radio", category: "Objects", example: "Das Radio spielt Musik.", audio: "https://example.com/audio/radio.mp3" },
  { word: "Uhr", pronunciation: "[uːɐ̯]", article: "die", english: "Watch", category: "Objects", example: "Die Uhr zeigt die Zeit.", audio: "https://example.com/audio/uhr2.mp3" },
  { word: "Schlüssel", pronunciation: "[ˈʃlʏsl̩]", article: "der", english: "Key", category: "Objects", example: "Der Schlüssel öffnet die Tür.", audio: "https://example.com/audio/schluessel.mp3" },
  { word: "Brille", pronunciation: "[ˈbʁɪlə]", article: "die", english: "Glasses", category: "Objects", example: "Die Brille hilft beim Sehen.", audio: "https://example.com/audio/brille.mp3" },
  { word: "Tasche", pronunciation: "[ˈtaʃə]", article: "die", english: "Bag", category: "Objects", example: "Die Tasche ist schwer.", audio: "https://example.com/audio/tasche.mp3" },
  { word: "Kamera", pronunciation: "[ˈkaməʁa]", article: "die", english: "Camera", category: "Objects", example: "Die Kamera macht Fotos.", audio: "https://example.com/audio/kamera.mp3" },
  { word: "Spiegel", pronunciation: "[ˈʃpiːɡl̩]", article: "der", english: "Mirror", category: "Objects", example: "Der Spiegel ist klar.", audio: "https://example.com/audio/spiegel.mp3" },
  { word: "Messer", pronunciation: "[ˈmɛsɐ]", article: "das", english: "Knife", category: "Objects", example: "Das Messer ist scharf.", audio: "https://example.com/audio/messer.mp3" },
  { word: "Gabel", pronunciation: "[ˈɡaːbl̩]", article: "die", english: "Fork", category: "Objects", example: "Die Gabel ist aus Metall.", audio: "https://example.com/audio/gabel.mp3" },
  { word: "Löffel", pronunciation: "[ˈlœfl̩]", article: "der", english: "Spoon", category: "Objects", example: "Der Löffel ist klein.", audio: "https://example.com/audio/loeffel.mp3" },
  { word: "Teller", pronunciation: "[ˈtɛlɐ]", article: "der", english: "Plate", category: "Objects", example: "Der Teller ist rund.", audio: "https://example.com/audio/teller.mp3" },
  { word: "Tasse", pronunciation: "[ˈtasə]", article: "die", english: "Cup", category: "Objects", example: "Die Tasse ist heiß.", audio: "https://example.com/audio/tasse.mp3" },

  // Places (20 words)
  { word: "Haus", pronunciation: "[haʊ̯s]", article: "das", english: "House", category: "Places", example: "Das Haus ist schön.", audio: "https://example.com/audio/haus.mp3" },
  { word: "Schule", pronunciation: "[ˈʃuːlə]", article: "die", english: "School", category: "Places", example: "Ich gehe zur Schule.", audio: "https://example.com/audio/schule.mp3" },
  { word: "Park", pronunciation: "[paʁk]", article: "der", english: "Park", category: "Places", example: "Der Park ist grün.", audio: "https://example.com/audio/park.mp3" },
  { word: "Stadt", pronunciation: "[ʃtat]", article: "die", english: "City", category: "Places", example: "Die Stadt ist groß.", audio: "https://example.com/audio/stadt.mp3" },
  { word: "Dorf", pronunciation: "[dɔʁf]", article: "das", english: "Village", category: "Places", example: "Das Dorf ist klein.", audio: "https://example.com/audio/dorf.mp3" },
  { word: "Krankenhaus", pronunciation: "[ˈkʁaŋkənhaʊ̯s]", article: "das", english: "Hospital", category: "Places", example: "Das Krankenhaus ist groß.", audio: "https://example.com/audio/krankenhaus.mp3" },
  { word: "Restaurant", pronunciation: "[ʁɛstoˈʁɑ̃ː]", article: "das", english: "Restaurant", category: "Places", example: "Das Restaurant ist teuer.", audio: "https://example.com/audio/restaurant.mp3" },
  { word: "Hotel", pronunciation: "[hoˈtɛl]", article: "das", english: "Hotel", category: "Places", example: "Das Hotel ist luxuriös.", audio: "https://example.com/audio/hotel.mp3" },
  { word: "Bahnhof", pronunciation: "[ˈbaːnhoːf]", article: "der", english: "Train station", category: "Places", example: "Der Bahnhof ist groß.", audio: "https://example.com/audio/bahnhof.mp3" },
  { word: "Flughafen", pronunciation: "[ˈfluːkhaːfən]", article: "der", english: "Airport", category: "Places", example: "Der Flughafen ist modern.", audio: "https://example.com/audio/flughafen.mp3" },
  { word: "Supermarkt", pronunciation: "[ˈzuːpɐmaʁkt]", article: "der", english: "Supermarket", category: "Places", example: "Der Supermarkt ist voll.", audio: "https://example.com/audio/supermarkt.mp3" },
  { word: "Bank", pronunciation: "[baŋk]", article: "die", english: "Bank", category: "Places", example: "Die Bank ist sicher.", audio: "https://example.com/audio/bank.mp3" },
  { word: "Post", pronunciation: "[pɔst]", article: "die", english: "Post office", category: "Places", example: "Die Post ist geöffnet.", audio: "https://example.com/audio/post.mp3" },
  { word: "Bibliothek", pronunciation: "[biblioˈteːk]", article: "die", english: "Library", category: "Places", example: "Die Bibliothek ist ruhig.", audio: "https://example.com/audio/bibliothek.mp3" },
  { word: "Museum", pronunciation: "[muˈzeːʊm]", article: "das", english: "Museum", category: "Places", example: "Das Museum ist interessant.", audio: "https://example.com/audio/museum.mp3" },
  { word: "Theater", pronunciation: "[teˈaːtɐ]", article: "das", english: "Theater", category: "Places", example: "Das Theater ist elegant.", audio: "https://example.com/audio/theater.mp3" },
  { word: "Kino", pronunciation: "[ˈkiːno]", article: "das", english: "Cinema", category: "Places", example: "Das Kino zeigt Filme.", audio: "https://example.com/audio/kino.mp3" },
  { word: "Strand", pronunciation: "[ʃtʁant]", article: "der", english: "Beach", category: "Places", example: "Der Strand ist sandig.", audio: "https://example.com/audio/strand.mp3" },
  { word: "Berg", pronunciation: "[bɛʁk]", article: "der", english: "Mountain", category: "Places", example: "Der Berg ist hoch.", audio: "https://example.com/audio/berg.mp3" },
  { word: "See", pronunciation: "[zeː]", article: "der", english: "Lake", category: "Places", example: "Der See ist klar.", audio: "https://example.com/audio/see.mp3" },

  // Transportation (20 words)
  { word: "Auto", pronunciation: "[ˈaʊ̯to]", article: "das", english: "Car", category: "Transportation", example: "Das Auto ist schnell.", audio: "https://example.com/audio/auto.mp3" },
  { word: "Zug", pronunciation: "[tsuːk]", article: "der", english: "Train", category: "Transportation", example: "Der Zug fährt.", audio: "https://example.com/audio/zug.mp3" },
  { word: "Bus", pronunciation: "[bʊs]", article: "der", english: "Bus", category: "Transportation", example: "Ich nehme den Bus.", audio: "https://example.com/audio/bus.mp3" },
  { word: "Fahrrad", pronunciation: "[ˈfaːɐ̯ʁaːt]", article: "das", english: "Bicycle", category: "Transportation", example: "Ich fahre Fahrrad.", audio: "https://example.com/audio/fahrrad.mp3" },
  { word: "Flugzeug", pronunciation: "[ˈfluːkt͡sɔɪ̯k]", article: "das", english: "Airplane", category: "Transportation", example: "Das Flugzeug fliegt.", audio: "https://example.com/audio/flugzeug.mp3" },
  { word: "Schiff", pronunciation: "[ʃɪf]", article: "das", english: "Ship", category: "Transportation", example: "Das Schiff segelt.", audio: "https://example.com/audio/schiff.mp3" },
  { word: "Motorrad", pronunciation: "[ˈmoːtoʁaːt]", article: "das", english: "Motorcycle", category: "Transportation", example: "Das Motorrad ist laut.", audio: "https://example.com/audio/motorrad.mp3" },
  { word: "Taxi", pronunciation: "[ˈtaksi]", article: "das", english: "Taxi", category: "Transportation", example: "Das Taxi ist gelb.", audio: "https://example.com/audio/taxi.mp3" },
  { word: "U-Bahn", pronunciation: "[ˈuːbaːn]", article: "die", english: "Subway", category: "Transportation", example: "Die U-Bahn ist schnell.", audio: "https://example.com/audio/ubahn.mp3" },
  { word: "Straßenbahn", pronunciation: "[ˈʃtʁaːsənbaːn]", article: "die", english: "Tram", category: "Transportation", example: "Die Straßenbahn fährt.", audio: "https://example.com/audio/strassenbahn.mp3" },
  { word: "Roller", pronunciation: "[ˈʁɔlɐ]", article: "der", english: "Scooter", category: "Transportation", example: "Der Roller ist praktisch.", audio: "https://example.com/audio/roller.mp3" },
  { word: "Skateboard", pronunciation: "[ˈskeɪtbɔːɐ̯t]", article: "das", english: "Skateboard", category: "Transportation", example: "Das Skateboard ist cool.", audio: "https://example.com/audio/skateboard.mp3" },
  { word: "Boot", pronunciation: "[boːt]", article: "das", english: "Boat", category: "Transportation", example: "Das Boot schwimmt.", audio: "https://example.com/audio/boot.mp3" },
  { word: "Hubschrauber", pronunciation: "[ˈhuːpʃʁaʊ̯bɐ]", article: "der", english: "Helicopter", category: "Transportation", example: "Der Hubschrauber fliegt.", audio: "https://example.com/audio/hubschrauber.mp3" },
  { word: "Lastwagen", pronunciation: "[ˈlastvaːɡən]", article: "der", english: "Truck", category: "Transportation", example: "Der Lastwagen ist groß.", audio: "https://example.com/audio/lastwagen.mp3" },
  { word: "Ambulanz", pronunciation: "[ambuˈlant͡s]", article: "die", english: "Ambulance", category: "Transportation", example: "Die Ambulanz ist schnell.", audio: "https://example.com/audio/ambulanz.mp3" },
  { word: "Feuerwehr", pronunciation: "[ˈfɔɪ̯ɐveːɐ̯]", article: "die", english: "Fire truck", category: "Transportation", example: "Die Feuerwehr hilft.", audio: "https://example.com/audio/feuerwehr.mp3" },
  { word: "Polizeiauto", pronunciation: "[poliˈt͡saɪ̯aʊ̯to]", article: "das", english: "Police car", category: "Transportation", example: "Das Polizeiauto ist blau.", audio: "https://example.com/audio/polizeiauto.mp3" },
  { word: "Lieferwagen", pronunciation: "[ˈliːfɐvaːɡən]", article: "der", english: "Delivery van", category: "Transportation", example: "Der Lieferwagen bringt Pakete.", audio: "https://example.com/audio/lieferwagen.mp3" },
  { word: "Segway", pronunciation: "[ˈsɛɡveɪ]", article: "der", english: "Segway", category: "Transportation", example: "Der Segway ist modern.", audio: "https://example.com/audio/segway.mp3" },

  // Professions (20 words)
  { word: "Arzt", pronunciation: "[aːʁt͡st]", article: "der", english: "Doctor", category: "Professions", example: "Der Arzt hilft.", audio: "https://example.com/audio/arzt.mp3" },
  { word: "Lehrer", pronunciation: "[ˈleːʁɐ]", article: "der", english: "Teacher", category: "Professions", example: "Der Lehrer unterrichtet.", audio: "https://example.com/audio/lehrer.mp3" },
  { word: "Ingenieur", pronunciation: "[ɪnʒeˈni̯øːɐ̯]", article: "der", english: "Engineer", category: "Professions", example: "Der Ingenieur plant.", audio: "https://example.com/audio/ingenieur.mp3" },
  { word: "Koch", pronunciation: "[kɔx]", article: "der", english: "Cook", category: "Professions", example: "Der Koch kocht gut.", audio: "https://example.com/audio/koch.mp3" },
  { word: "Polizist", pronunciation: "[poliˈt͡sɪst]", article: "der", english: "Police officer", category: "Professions", example: "Der Polizist arbeitet.", audio: "https://example.com/audio/polizist.mp3" },
  { word: "Krankenschwester", pronunciation: "[ˈkʁaŋkənʃvɛstɐ]", article: "die", english: "Nurse", category: "Professions", example: "Die Krankenschwester hilft.", audio: "https://example.com/audio/krankenschwester.mp3" },
  { word: "Pilot", pronunciation: "[piˈloːt]", article: "der", english: "Pilot", category: "Professions", example: "Der Pilot fliegt.", audio: "https://example.com/audio/pilot.mp3" },
  { word: "Friseur", pronunciation: "[fʁiˈzøːɐ̯]", article: "der", english: "Hairdresser", category: "Professions", example: "Der Friseur schneidet Haare.", audio: "https://example.com/audio/friseur.mp3" },
  { word: "Mechaniker", pronunciation: "[meˈçaːnɪkɐ]", article: "der", english: "Mechanic", category: "Professions", example: "Der Mechaniker repariert.", audio: "https://example.com/audio/mechaniker.mp3" },
  { word: "Verkäufer", pronunciation: "[fɛɐ̯ˈkɔɪ̯fɐ]", article: "der", english: "Salesperson", category: "Professions", example: "Der Verkäufer verkauft.", audio: "https://example.com/audio/verkaeufer.mp3" },
  { word: "Anwalt", pronunciation: "[ˈanvalt]", article: "der", english: "Lawyer", category: "Professions", example: "Der Anwalt verteidigt.", audio: "https://example.com/audio/anwalt.mp3" },
  { word: "Architekt", pronunciation: "[aʁçiˈtɛkt]", article: "der", english: "Architect", category: "Professions", example: "Der Architekt entwirft.", audio: "https://example.com/audio/architekt.mp3" },
  { word: "Journalist", pronunciation: "[ʒuʁnaˈlɪst]", article: "der", english: "Journalist", category: "Professions", example: "Der Journalist schreibt.", audio: "https://example.com/audio/journalist.mp3" },
  { word: "Musiker", pronunciation: "[ˈmuːzɪkɐ]", article: "der", english: "Musician", category: "Professions", example: "Der Musiker spielt.", audio: "https://example.com/audio/musiker.mp3" },
  { word: "Künstler", pronunciation: "[ˈkʏnstlɐ]", article: "der", english: "Artist", category: "Professions", example: "Der Künstler malt.", audio: "https://example.com/audio/kuenstler.mp3" },
  { word: "Bäcker", pronunciation: "[ˈbɛkɐ]", article: "der", english: "Baker", category: "Professions", example: "Der Bäcker backt Brot.", audio: "https://example.com/audio/baecker.mp3" },
  { word: "Gärtner", pronunciation: "[ˈɡɛʁtnɐ]", article: "der", english: "Gardener", category: "Professions", example: "Der Gärtner pflanzt.", audio: "https://example.com/audio/gaertner.mp3" },
  { word: "Elektriker", pronunciation: "[eˈlɛktʁɪkɐ]", article: "der", english: "Electrician", category: "Professions", example: "Der Elektriker repariert.", audio: "https://example.com/audio/elektriker.mp3" },
  { word: "Zahnarzt", pronunciation: "[ˈt͡saːnaʁt͡st]", article: "der", english: "Dentist", category: "Professions", example: "Der Zahnarzt behandelt Zähne.", audio: "https://example.com/audio/zahnarzt.mp3" },
  { word: "Tierarzt", pronunciation: "[ˈtiːɐ̯aʁt͡st]", article: "der", english: "Veterinarian", category: "Professions", example: "Der Tierarzt hilft Tieren.", audio: "https://example.com/audio/tierarzt.mp3" },

  // Adjectives (20 words)
  { word: "Groß", pronunciation: "[ɡʁoːs]", article: "", english: "Big", category: "Adjectives", example: "Das Haus ist groß.", audio: "https://example.com/audio/gross.mp3" },
  { word: "Klein", pronunciation: "[klaɪ̯n]", article: "", english: "Small", category: "Adjectives", example: "Das Auto ist klein.", audio: "https://example.com/audio/klein.mp3" },
  { word: "Schnell", pronunciation: "[ʃnɛl]", article: "", english: "Fast", category: "Adjectives", example: "Der Zug ist schnell.", audio: "https://example.com/audio/schnell.mp3" },
  { word: "Langsam", pronunciation: "[ˈlaŋzaːm]", article: "", english: "Slow", category: "Adjectives", example: "Die Schildkröte ist langsam.", audio: "https://example.com/audio/langsam.mp3" },
  { word: "Schön", pronunciation: "[ʃøːn]", article: "", english: "Beautiful", category: "Adjectives", example: "Der Park ist schön.", audio: "https://example.com/audio/schoen.mp3" },
  { word: "Hässlich", pronunciation: "[ˈhɛslɪç]", article: "", english: "Ugly", category: "Adjectives", example: "Das Bild ist hässlich.", audio: "https://example.com/audio/haesslich.mp3" },
  { word: "Jung", pronunciation: "[jʊŋ]", article: "", english: "Young", category: "Adjectives", example: "Das Kind ist jung.", audio: "https://example.com/audio/jung.mp3" },
  { word: "Alt", pronunciation: "[alt]", article: "", english: "Old", category: "Adjectives", example: "Der Mann ist alt.", audio: "https://example.com/audio/alt.mp3" },
  { word: "Neu", pronunciation: "[nɔɪ̯]", article: "", english: "New", category: "Adjectives", example: "Das Auto ist neu.", audio: "https://example.com/audio/neu.mp3" },
  { word: "Heiß", pronunciation: "[haɪ̯s]", article: "", english: "Hot", category: "Adjectives", example: "Der Kaffee ist heiß.", audio: "https://example.com/audio/heiss.mp3" },
  { word: "Kalt", pronunciation: "[kalt]", article: "", english: "Cold", category: "Adjectives", example: "Das Wasser ist kalt.", audio: "https://example.com/audio/kalt.mp3" },
  { word: "Warm", pronunciation: "[vaʁm]", article: "", english: "Warm", category: "Adjectives", example: "Die Suppe ist warm.", audio: "https://example.com/audio/warm.mp3" },
  { word: "Kühl", pronunciation: "[kyːl]", article: "", english: "Cool", category: "Adjectives", example: "Der Wind ist kühl.", audio: "https://example.com/audio/kuehl.mp3" },
  { word: "Hell", pronunciation: "[hɛl]", article: "", english: "Bright", category: "Adjectives", example: "Das Licht ist hell.", audio: "https://example.com/audio/hell.mp3" },
  { word: "Dunkel", pronunciation: "[ˈdʊŋkl̩]", article: "", english: "Dark", category: "Adjectives", example: "Die Nacht ist dunkel.", audio: "https://example.com/audio/dunkel.mp3" },
  { word: "Laut", pronunciation: "[laʊ̯t]", article: "", english: "Loud", category: "Adjectives", example: "Die Musik ist laut.", audio: "https://example.com/audio/laut.mp3" },
  { word: "Leise", pronunciation: "[ˈlaɪ̯zə]", article: "", english: "Quiet", category: "Adjectives", example: "Das Baby ist leise.", audio: "https://example.com/audio/leise.mp3" },
  { word: "Stark", pronunciation: "[ʃtaʁk]", article: "", english: "Strong", category: "Adjectives", example: "Der Mann ist stark.", audio: "https://example.com/audio/stark.mp3" },
  { word: "Schwach", pronunciation: "[ʃvaːx]", article: "", english: "Weak", category: "Adjectives", example: "Das Signal ist schwach.", audio: "https://example.com/audio/schwach.mp3" },
  { word: "Süß", pronunciation: "[zyːs]", article: "", english: "Sweet", category: "Adjectives", example: "Der Kuchen ist süß.", audio: "https://example.com/audio/suess.mp3" },

  // Verbs (20 words)
  { word: "Essen", pronunciation: "[ˈɛsn̩]", article: "", english: "To eat", category: "Verbs", example: "Ich esse einen Apfel.", audio: "https://example.com/audio/essen.mp3" },
  { word: "Trinken", pronunciation: "[ˈtʁɪŋkən]", article: "", english: "To drink", category: "Verbs", example: "Ich trinke Wasser.", audio: "https://example.com/audio/trinken.mp3" },
  { word: "Lesen", pronunciation: "[ˈleːzn̩]", article: "", english: "To read", category: "Verbs", example: "Ich lese ein Buch.", audio: "https://example.com/audio/lesen.mp3" },
  { word: "Schreiben", pronunciation: "[ˈʃʁaɪ̯bn̩]", article: "", english: "To write", category: "Verbs", example: "Ich schreibe einen Brief.", audio: "https://example.com/audio/schreiben.mp3" },
  { word: "Laufen", pronunciation: "[ˈlaʊ̯fn̩]", article: "", english: "To run", category: "Verbs", example: "Ich laufe schnell.", audio: "https://example.com/audio/laufen.mp3" },
  { word: "Gehen", pronunciation: "[ˈɡeːən]", article: "", english: "To go", category: "Verbs", example: "Ich gehe nach Hause.", audio: "https://example.com/audio/gehen.mp3" },
  { word: "Kommen", pronunciation: "[ˈkɔmən]", article: "", english: "To come", category: "Verbs", example: "Ich komme morgen.", audio: "https://example.com/audio/kommen.mp3" },
  { word: "Sehen", pronunciation: "[ˈzeːən]", article: "", english: "To see", category: "Verbs", example: "Ich sehe dich.", audio: "https://example.com/audio/sehen.mp3" },
  { word: "Hören", pronunciation: "[ˈhøːʁən]", article: "", english: "To hear", category: "Verbs", example: "Ich höre Musik.", audio: "https://example.com/audio/hoeren.mp3" },
  { word: "Sprechen", pronunciation: "[ˈʃpʁɛçən]", article: "", english: "To speak", category: "Verbs", example: "Ich spreche Deutsch.", audio: "https://example.com/audio/sprechen.mp3" },
  { word: "Verstehen", pronunciation: "[fɛɐ̯ˈʃteːən]", article: "", english: "To understand", category: "Verbs", example: "Ich verstehe dich.", audio: "https://example.com/audio/verstehen.mp3" },
  { word: "Lernen", pronunciation: "[ˈlɛʁnən]", article: "", english: "To learn", category: "Verbs", example: "Ich lerne Deutsch.", audio: "https://example.com/audio/lernen.mp3" },
  { word: "Arbeiten", pronunciation: "[ˈaʁbaɪ̯tən]", article: "", english: "To work", category: "Verbs", example: "Ich arbeite im Büro.", audio: "https://example.com/audio/arbeiten.mp3" },
  { word: "Spielen", pronunciation: "[ˈʃpiːlən]", article: "", english: "To play", category: "Verbs", example: "Ich spiele Fußball.", audio: "https://example.com/audio/spielen.mp3" },
  { word: "Schlafen", pronunciation: "[ˈʃlaːfən]", article: "", english: "To sleep", category: "Verbs", example: "Ich schlafe gut.", audio: "https://example.com/audio/schlafen.mp3" },
  { word: "Wachen", pronunciation: "[ˈvaxən]", article: "", english: "To wake up", category: "Verbs", example: "Ich wache früh auf.", audio: "https://example.com/audio/wachen.mp3" },
  { word: "Kochen", pronunciation: "[ˈkɔxən]", article: "", english: "To cook", category: "Verbs", example: "Ich koche Abendessen.", audio: "https://example.com/audio/kochen.mp3" },
  { word: "Kaufen", pronunciation: "[ˈkaʊ̯fən]", article: "", english: "To buy", category: "Verbs", example: "Ich kaufe Brot.", audio: "https://example.com/audio/kaufen.mp3" },
  { word: "Verkaufen", pronunciation: "[fɛɐ̯ˈkaʊ̯fən]", article: "", english: "To sell", category: "Verbs", example: "Ich verkaufe mein Auto.", audio: "https://example.com/audio/verkaufen.mp3" },
  { word: "Helfen", pronunciation: "[ˈhɛlfən]", article: "", english: "To help", category: "Verbs", example: "Ich helfe dir.", audio: "https://example.com/audio/helfen.mp3" },

  // Body Parts (20 words)
  { word: "Kopf", pronunciation: "[kɔpf]", article: "der", english: "Head", category: "Body Parts", example: "Mein Kopf tut weh.", audio: "https://example.com/audio/kopf.mp3" },
  { word: "Hand", pronunciation: "[hant]", article: "die", english: "Hand", category: "Body Parts", example: "Meine Hand ist kalt.", audio: "https://example.com/audio/hand.mp3" },
  { word: "Fuß", pronunciation: "[fuːs]", article: "der", english: "Foot", category: "Body Parts", example: "Mein Fuß schmerzt.", audio: "https://example.com/audio/fuss.mp3" },
  { word: "Auge", pronunciation: "[ˈaʊ̯ɡə]", article: "das", english: "Eye", category: "Body Parts", example: "Das Auge sieht.", audio: "https://example.com/audio/auge.mp3" },
  { word: "Mund", pronunciation: "[mʊnt]", article: "der", english: "Mouth", category: "Body Parts", example: "Der Mund spricht.", audio: "https://example.com/audio/mund.mp3" },
  { word: "Nase", pronunciation: "[ˈnaːzə]", article: "die", english: "Nose", category: "Body Parts", example: "Die Nase riecht.", audio: "https://example.com/audio/nase.mp3" },
  { word: "Ohr", pronunciation: "[oːɐ̯]", article: "das", english: "Ear", category: "Body Parts", example: "Das Ohr hört.", audio: "https://example.com/audio/ohr.mp3" },
  { word: "Arm", pronunciation: "[aʁm]", article: "der", english: "Arm", category: "Body Parts", example: "Mein Arm ist stark.", audio: "https://example.com/audio/arm.mp3" },
  { word: "Bein", pronunciation: "[baɪ̯n]", article: "das", english: "Leg", category: "Body Parts", example: "Mein Bein ist lang.", audio: "https://example.com/audio/bein.mp3" },
  { word: "Finger", pronunciation: "[ˈfɪŋɐ]", article: "der", english: "Finger", category: "Body Parts", example: "Der Finger zeigt.", audio: "https://example.com/audio/finger.mp3" },
  { word: "Zeh", pronunciation: "[t͡seː]", article: "der", english: "Toe", category: "Body Parts", example: "Der Zeh ist klein.", audio: "https://example.com/audio/zeh.mp3" },
  { word: "Haar", pronunciation: "[haːɐ̯]", article: "das", english: "Hair", category: "Body Parts", example: "Das Haar ist blond.", audio: "https://example.com/audio/haar.mp3" },
  { word: "Zahn", pronunciation: "[t͡saːn]", article: "der", english: "Tooth", category: "Body Parts", example: "Der Zahn ist weiß.", audio: "https://example.com/audio/zahn.mp3" },
  { word: "Hals", pronunciation: "[hals]", article: "der", english: "Neck", category: "Body Parts", example: "Der Hals ist lang.", audio: "https://example.com/audio/hals.mp3" },
  { word: "Schulter", pronunciation: "[ˈʃʊltɐ]", article: "die", english: "Shoulder", category: "Body Parts", example: "Die Schulter ist breit.", audio: "https://example.com/audio/schulter.mp3" },
  { word: "Rücken", pronunciation: "[ˈʁʏkən]", article: "der", english: "Back", category: "Body Parts", example: "Der Rücken schmerzt.", audio: "https://example.com/audio/ruecken.mp3" },
  { word: "Bauch", pronunciation: "[baʊ̯x]", article: "der", english: "Belly", category: "Body Parts", example: "Der Bauch ist voll.", audio: "https://example.com/audio/bauch.mp3" },
  { word: "Brust", pronunciation: "[bʁʊst]", article: "die", english: "Chest", category: "Body Parts", example: "Die Brust ist stark.", audio: "https://example.com/audio/brust.mp3" },
  { word: "Herz", pronunciation: "[hɛʁt͡s]", article: "das", english: "Heart", category: "Body Parts", example: "Das Herz schlägt.", audio: "https://example.com/audio/herz.mp3" },
  { word: "Gehirn", pronunciation: "[ɡəˈhɪʁn]", article: "das", english: "Brain", category: "Body Parts", example: "Das Gehirn denkt.", audio: "https://example.com/audio/gehirn.mp3" },

  // School (20 words)
  { word: "Schule", pronunciation: "[ˈʃuːlə]", article: "die", english: "School", category: "School", example: "Ich gehe zur Schule.", audio: "https://example.com/audio/schule2.mp3" },
  { word: "Lehrer", pronunciation: "[ˈleːʁɐ]", article: "der", english: "Teacher", category: "School", example: "Der Lehrer unterrichtet.", audio: "https://example.com/audio/lehrer2.mp3" },
  { word: "Schüler", pronunciation: "[ˈʃyːlɐ]", article: "der", english: "Student", category: "School", example: "Der Schüler lernt.", audio: "https://example.com/audio/schueler.mp3" },
  { word: "Buch", pronunciation: "[buːx]", article: "das", english: "Book", category: "School", example: "Ich lese ein Buch.", audio: "https://example.com/audio/buch2.mp3" },
  { word: "Heft", pronunciation: "[hɛft]", article: "das", english: "Notebook", category: "School", example: "Ich schreibe im Heft.", audio: "https://example.com/audio/heft.mp3" },
  { word: "Stift", pronunciation: "[ʃtɪft]", article: "der", english: "Pen", category: "School", example: "Ich brauche einen Stift.", audio: "https://example.com/audio/stift2.mp3" },
  { word: "Bleistift", pronunciation: "[ˈblaɪ̯ʃtɪft]", article: "der", english: "Pencil", category: "School", example: "Der Bleistift ist spitz.", audio: "https://example.com/audio/bleistift.mp3" },
  { word: "Radiergummi", pronunciation: "[ʁaˈdiːɐ̯ɡʊmi]", article: "der", english: "Eraser", category: "School", example: "Der Radiergummi löscht.", audio: "https://example.com/audio/radiergummi.mp3" },
  { word: "Lineal", pronunciation: "[liˈneaːl]", article: "das", english: "Ruler", category: "School", example: "Das Lineal ist gerade.", audio: "https://example.com/audio/lineal.mp3" },
  { word: "Tafel", pronunciation: "[ˈtaːfl̩]", article: "die", english: "Blackboard", category: "School", example: "Die Tafel ist schwarz.", audio: "https://example.com/audio/tafel.mp3" },
  { word: "Kreide", pronunciation: "[ˈkʁaɪ̯də]", article: "die", english: "Chalk", category: "School", example: "Die Kreide ist weiß.", audio: "https://example.com/audio/kreide.mp3" },
  { word: "Schulranzen", pronunciation: "[ˈʃuːlʁant͡sən]", article: "der", english: "School bag", category: "School", example: "Der Schulranzen ist schwer.", audio: "https://example.com/audio/schulranzen.mp3" },
  { word: "Klassenzimmer", pronunciation: "[ˈklasənˌt͡sɪmɐ]", article: "das", english: "Classroom", category: "School", example: "Das Klassenzimmer ist groß.", audio: "https://example.com/audio/klassenzimmer.mp3" },
  { word: "Hausaufgaben", pronunciation: "[ˈhaʊ̯sˌaʊ̯fɡaːbən]", article: "die", english: "Homework", category: "School", example: "Die Hausaufgaben sind schwer.", audio: "https://example.com/audio/hausaufgaben.mp3" },
  { word: "Prüfung", pronunciation: "[ˈpʁyːfʊŋ]", article: "die", english: "Exam", category: "School", example: "Die Prüfung ist morgen.", audio: "https://example.com/audio/pruefung.mp3" },
  { word: "Note", pronunciation: "[ˈnoːtə]", article: "die", english: "Grade", category: "School", example: "Die Note ist gut.", audio: "https://example.com/audio/note.mp3" },
  { word: "Pause", pronunciation: "[ˈpaʊ̯zə]", article: "die", english: "Break", category: "School", example: "Die Pause ist kurz.", audio: "https://example.com/audio/pause.mp3" },
  { word: "Unterricht", pronunciation: "[ˈʊntɐʁɪçt]", article: "der", english: "Lesson", category: "School", example: "Der Unterricht beginnt.", audio: "https://example.com/audio/unterricht.mp3" },
  { word: "Mathematik", pronunciation: "[matemaˈtiːk]", article: "die", english: "Mathematics", category: "School", example: "Mathematik ist schwer.", audio: "https://example.com/audio/mathematik.mp3" },
  { word: "Geschichte", pronunciation: "[ɡəˈʃɪçtə]", article: "die", english: "History", category: "School", example: "Geschichte ist interessant.", audio: "https://example.com/audio/geschichte.mp3" },

  // Hobbies (20 words)
  { word: "Lesen", pronunciation: "[ˈleːzn̩]", article: "", english: "Reading", category: "Hobbies", example: "Ich liebe Lesen.", audio: "https://example.com/audio/lesen2.mp3" },
  { word: "Sport", pronunciation: "[ʃpɔʁt]", article: "der", english: "Sport", category: "Hobbies", example: "Ich mache Sport.", audio: "https://example.com/audio/sport.mp3" },
  { word: "Musik", pronunciation: "[muˈziːk]", article: "die", english: "Music", category: "Hobbies", example: "Ich höre Musik.", audio: "https://example.com/audio/musik.mp3" },
  { word: "Malen", pronunciation: "[ˈmaːlən]", article: "", english: "Painting", category: "Hobbies", example: "Ich male ein Bild.", audio: "https://example.com/audio/malen.mp3" },
  { word: "Tanzen", pronunciation: "[ˈtant͡sn̩]", article: "", english: "Dancing", category: "Hobbies", example: "Ich tanze gern.", audio: "https://example.com/audio/tanzen.mp3" },
  { word: "Singen", pronunciation: "[ˈzɪŋən]", article: "", english: "Singing", category: "Hobbies", example: "Ich singe laut.", audio: "https://example.com/audio/singen.mp3" },
  { word: "Kochen", pronunciation: "[ˈkɔxən]", article: "", english: "Cooking", category: "Hobbies", example: "Ich koche gern.", audio: "https://example.com/audio/kochen2.mp3" },
  { word: "Gärtnern", pronunciation: "[ˈɡɛʁtnɐn]", article: "", english: "Gardening", category: "Hobbies", example: "Ich liebe Gärtnern.", audio: "https://example.com/audio/gaertnern.mp3" },
  { word: "Fotografieren", pronunciation: "[fotoɡʁaˈfiːʁən]", article: "", english: "Photography", category: "Hobbies", example: "Ich fotografiere gern.", audio: "https://example.com/audio/fotografieren.mp3" },
  { word: "Reisen", pronunciation: "[ˈʁaɪ̯zn̩]", article: "", english: "Traveling", category: "Hobbies", example: "Ich reise viel.", audio: "https://example.com/audio/reisen.mp3" },
  { word: "Schwimmen", pronunciation: "[ˈʃvɪmən]", article: "", english: "Swimming", category: "Hobbies", example: "Ich schwimme gern.", audio: "https://example.com/audio/schwimmen.mp3" },
  { word: "Wandern", pronunciation: "[ˈvandɐn]", article: "", english: "Hiking", category: "Hobbies", example: "Ich wandere oft.", audio: "https://example.com/audio/wandern.mp3" },
  { word: "Radfahren", pronunciation: "[ˈʁaːtfaːʁən]", article: "", english: "Cycling", category: "Hobbies", example: "Ich fahre Rad.", audio: "https://example.com/audio/radfahren.mp3" },
  { word: "Fußball", pronunciation: "[ˈfuːsbal]", article: "der", english: "Football", category: "Hobbies", example: "Ich spiele Fußball.", audio: "https://example.com/audio/fussball.mp3" },
  { word: "Tennis", pronunciation: "[ˈtɛnɪs]", article: "das", english: "Tennis", category: "Hobbies", example: "Ich spiele Tennis.", audio: "https://example.com/audio/tennis.mp3" },
  { word: "Schach", pronunciation: "[ʃax]", article: "das", english: "Chess", category: "Hobbies", example: "Ich spiele Schach.", audio: "https://example.com/audio/schach.mp3" },
  { word: "Karten", pronunciation: "[ˈkaʁtən]", article: "die", english: "Cards", category: "Hobbies", example: "Ich spiele Karten.", audio: "https://example.com/audio/karten.mp3" },
  { word: "Sammeln", pronunciation: "[ˈzaml̩n]", article: "", english: "Collecting", category: "Hobbies", example: "Ich sammle Münzen.", audio: "https://example.com/audio/sammeln.mp3" },
  { word: "Basteln", pronunciation: "[ˈbastl̩n]", article: "", english: "Crafting", category: "Hobbies", example: "Ich bastle gern.", audio: "https://example.com/audio/basteln.mp3" },
  { word: "Nähen", pronunciation: "[ˈnɛːən]", article: "", english: "Sewing", category: "Hobbies", example: "Ich nähe Kleider.", audio: "https://example.com/audio/naehen.mp3" },

  // Emotions (20 words)
  { word: "Glücklich", pronunciation: "[ˈɡlʏklɪç]", article: "", english: "Happy", category: "Emotions", example: "Ich bin glücklich.", audio: "https://example.com/audio/gluecklich.mp3" },
  { word: "Traurig", pronunciation: "[ˈtʁaʊ̯ʁɪç]", article: "", english: "Sad", category: "Emotions", example: "Er ist traurig.", audio: "https://example.com/audio/traurig.mp3" },
  { word: "Müde", pronunciation: "[ˈmyːdə]", article: "", english: "Tired", category: "Emotions", example: "Ich bin müde.", audio: "https://example.com/audio/muede.mp3" },
  { word: "Wütend", pronunciation: "[ˈvyːtənt]", article: "", english: "Angry", category: "Emotions", example: "Sie ist wütend.", audio: "https://example.com/audio/wuetend.mp3" },
  { word: "Froh", pronunciation: "[fʁoː]", article: "", english: "Glad", category: "Emotions", example: "Ich bin froh.", audio: "https://example.com/audio/froh.mp3" },
  { word: "Aufgeregt", pronunciation: "[ˈaʊ̯fɡəʁeːkt]", article: "", english: "Excited", category: "Emotions", example: "Ich bin aufgeregt.", audio: "https://example.com/audio/aufgeregt.mp3" },
  { word: "Nervös", pronunciation: "[nɛʁˈvøːs]", article: "", english: "Nervous", category: "Emotions", example: "Er ist nervös.", audio: "https://example.com/audio/nervoes.mp3" },
  { word: "Ruhig", pronunciation: "[ˈʁuːɪç]", article: "", english: "Calm", category: "Emotions", example: "Sie ist ruhig.", audio: "https://example.com/audio/ruhig.mp3" },
  { word: "Ängstlich", pronunciation: "[ˈɛŋstlɪç]", article: "", english: "Anxious", category: "Emotions", example: "Das Kind ist ängstlich.", audio: "https://example.com/audio/aengstlich.mp3" },
  { word: "Stolz", pronunciation: "[ʃtɔlt͡s]", article: "", english: "Proud", category: "Emotions", example: "Ich bin stolz.", audio: "https://example.com/audio/stolz.mp3" },
  { word: "Eifersüchtig", pronunciation: "[ˈaɪ̯fɐzyːçtɪç]", article: "", english: "Jealous", category: "Emotions", example: "Er ist eifersüchtig.", audio: "https://example.com/audio/eifersuechtig.mp3" },
  { word: "Verliebt", pronunciation: "[fɛɐ̯ˈliːpt]", article: "", english: "In love", category: "Emotions", example: "Sie ist verliebt.", audio: "https://example.com/audio/verliebt.mp3" },
  { word: "Enttäuscht", pronunciation: "[ɛntˈtɔɪ̯ʃt]", article: "", english: "Disappointed", category: "Emotions", example: "Ich bin enttäuscht.", audio: "https://example.com/audio/enttaeuscht.mp3" },
  { word: "Überrascht", pronunciation: "[yːbɐˈʁaʃt]", article: "", english: "Surprised", category: "Emotions", example: "Er ist überrascht.", audio: "https://example.com/audio/ueberrascht.mp3" },
  { word: "Verwirrt", pronunciation: "[fɛɐ̯ˈvɪʁt]", article: "", english: "Confused", category: "Emotions", example: "Ich bin verwirrt.", audio: "https://example.com/audio/verwirrt.mp3" },
  { word: "Entspannt", pronunciation: "[ɛntˈʃpant]", article: "", english: "Relaxed", category: "Emotions", example: "Sie ist entspannt.", audio: "https://example.com/audio/entspannt.mp3" },
  { word: "Gestresst", pronunciation: "[ɡəˈʃtʁɛst]", article: "", english: "Stressed", category: "Emotions", example: "Er ist gestresst.", audio: "https://example.com/audio/gestresst.mp3" },
  { word: "Hoffnungsvoll", pronunciation: "[ˈhɔfnʊŋsfɔl]", article: "", english: "Hopeful", category: "Emotions", example: "Ich bin hoffnungsvoll.", audio: "https://example.com/audio/hoffnungsvoll.mp3" },
  { word: "Einsam", pronunciation: "[ˈaɪ̯nzaːm]", article: "", english: "Lonely", category: "Emotions", example: "Er fühlt sich einsam.", audio: "https://example.com/audio/einsam.mp3" },
  { word: "Zufrieden", pronunciation: "[t͡suˈfʁiːdən]", article: "", english: "Content", category: "Emotions", example: "Ich bin zufrieden.", audio: "https://example.com/audio/zufrieden.mp3" },
];
