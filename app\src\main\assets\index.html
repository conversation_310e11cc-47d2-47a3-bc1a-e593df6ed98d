<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Learn German App</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="dictionary.js"></script>
  <style>
    canvas { border: 2px solid #000; }
    .hidden { display: none; }
  </style>
</head>
<body class="bg-gray-100 font-sans">
  <header class="bg-green-600 text-white p-4">
    <h1 class="text-2xl font-bold">Learn German App</h1>
    <nav class="mt-2">
      <a href="#dictionary" class="text-white hover:underline mx-2">Dictionary</a>
      <a href="#writing" class="text-white hover:underline mx-2">Writing Practice</a>
      <a href="#flashcards" class="text-white hover:underline mx-2">Flashcards</a>
      <a href="#sentence" class="text-white hover:underline mx-2">Sentence Making</a>
      <a href="#quiz" class="text-white hover:underline mx-2">Quiz</a>
    </nav>
  </header>

  <main class="container mx-auto p-4">
    <!-- Dictionary Section -->
    <section id="dictionary" class="mb-8">
      <h2 class="text-xl font-semibold mb-4">Dictionary</h2>

      <!-- Search and Filter Controls -->
      <div class="mb-4 flex flex-col md:flex-row gap-4">
        <input type="text" id="searchInput" placeholder="Search German words..." class="border p-2 flex-1">
        <select id="categoryFilter" class="border p-2 md:w-48">
          <option value="">All Categories</option>
          <!-- Categories will be populated dynamically -->
        </select>
      </div>

      <!-- Dynamic Stats Display -->
      <div class="mb-4 p-3 bg-blue-50 rounded">
        <p id="dictionaryStats" class="text-blue-800 font-medium">400 words found | 400 total words in 20 categories</p>
        <div class="mt-2">
          <div class="bg-blue-200 rounded-full h-2">
            <div id="progressBar" class="bg-blue-600 h-2 rounded-full" style="width: 100%"></div>
          </div>
        </div>
      </div>

      <div id="dictionaryResults" class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Dictionary entries will be populated here -->
      </div>
    </section>

    <!-- Writing Practice Section -->
    <section id="writing" class="mb-8 hidden">
      <h2 class="text-xl font-semibold mb-4">German Character Writing Practice</h2>
      <div class="flex flex-col items-center">
        <p class="text-4xl mb-4" id="currentChar">ä</p>
        <canvas id="writingCanvas" width="200" height="200"></canvas>
        <div class="mt-4">
          <button id="clearCanvas" class="bg-green-500 text-white px-4 py-2 mr-2">Clear</button>
          <button id="nextChar" class="bg-green-500 text-white px-4 py-2">Next Character</button>
        </div>
      </div>
    </section>

    <!-- Flashcards Section -->
    <section id="flashcards" class="mb-8 hidden">
      <h2 class="text-xl font-semibold mb-4">Flashcards</h2>

      <!-- Progress indicator for flashcards -->
      <div class="mb-4 p-3 bg-green-50 rounded">
        <p id="flashcardProgress" class="text-green-800 font-medium">Card 1 of 400</p>
        <div class="mt-2">
          <div class="bg-green-200 rounded-full h-2">
            <div id="flashcardProgressBar" class="bg-green-600 h-2 rounded-full" style="width: 0.25%"></div>
          </div>
        </div>
      </div>

      <div class="border p-4 bg-white rounded shadow text-center">
        <p class="text-2xl mb-2" id="flashcardWord">Apfel</p>
        <p class="text-lg mb-2" id="flashcardPronunciation">[ˈapfəl]</p>
        <p class="text-lg mb-2 cursor-pointer" id="flashcardTranslation" onclick="toggleTranslation()">(Tap to see translation)</p>
        <p class="text-lg mb-2 hidden" id="flashcardEnglish">Apple</p>
        <p class="text-lg mb-2" id="flashcardExample">Ich esse einen Apfel.</p>
        <p class="text-md mb-2 text-gray-500" id="flashcardExampleEnglish">I eat an apple.</p>
        <button class="mt-2 bg-blue-500 text-white px-3 py-1 rounded" onclick="speakText(document.getElementById('flashcardExample').textContent, 'de-DE')">🔊 Example</button>
      </div>
      <div class="flex justify-center mt-4">
        <button id="prevFlashcard" class="bg-green-500 text-white px-4 py-2 mr-2">Previous</button>
        <button id="speakGerman" class="bg-green-500 text-white px-4 py-2 mr-2">🔊 German</button>
        <button id="speakEnglish" class="bg-green-500 text-white px-4 py-2 mr-2">🔊 English</button>
        <button id="nextFlashcard" class="bg-green-500 text-white px-4 py-2">Next</button>
      </div>
    </section>

    <!-- Sentence Making Section -->
    <section id="sentence" class="mb-8 hidden">
      <h2 class="text-xl font-semibold mb-4">Sentence Making</h2>
      <p class="mb-4">Drag and drop the words to form a correct German sentence</p>
      <div id="sentencePrompt" class="mb-4">Make a sentence: "I eat an apple."</div>
      <div id="wordBank" class="flex flex-wrap gap-2 mb-4">
        <!-- Word tiles will be populated here -->
      </div>
      <div id="sentenceArea" class="border p-4 bg-white rounded shadow min-h-[50px] mb-4"></div>
      <button id="checkSentence" class="bg-green-500 text-white px-4 py-2 mr-2">Check Sentence</button>
      <button id="speakSentence" class="bg-green-500 text-white px-4 py-2">🔊 Speak Sentence</button>
      <p id="sentenceFeedback" class="mt-2"></p>
    </section>

    <!-- Quiz Section -->
    <section id="quiz" class="mb-8 hidden">
      <h2 class="text-xl font-semibold mb-4">German Quiz</h2>

      <!-- Quiz Progress -->
      <div class="mb-4 p-3 bg-purple-50 rounded">
        <p id="quizProgress" class="text-purple-800 font-medium">Question 1 of 10</p>
        <div class="mt-2">
          <div class="bg-purple-200 rounded-full h-2">
            <div id="quizProgressBar" class="bg-purple-600 h-2 rounded-full" style="width: 10%"></div>
          </div>
        </div>
      </div>

      <div class="border p-6 bg-white rounded shadow">
        <p class="text-lg mb-4" id="quizQuestion">What does "Apfel" mean in English?</p>
        <div id="quizOptions" class="space-y-2">
          <!-- Quiz options will be populated here -->
        </div>
        <div class="mt-4">
          <button id="submitAnswer" class="bg-purple-500 text-white px-4 py-2 mr-2">Submit Answer</button>
          <button id="nextQuestion" class="bg-purple-500 text-white px-4 py-2 hidden">Next Question</button>
        </div>
        <p id="quizFeedback" class="mt-4"></p>
      </div>

      <div class="mt-4">
        <button id="startQuiz" class="bg-purple-600 text-white px-6 py-2">Start New Quiz</button>
        <p id="quizScore" class="mt-2 text-lg font-semibold"></p>
      </div>
    </section>
  </main>

  <script>
    // Dictionary is loaded from dictionary.js

    // Get unique categories from dictionary
    const categories = [...new Set(dictionary.map(item => item.category))];

    // Initialize app variables
    let filteredDictionary = [...dictionary];
    let currentFlashcardIndex = 0;
    let currentQuizIndex = 0;
    let quizQuestions = [];
    let quizScore = 0;
    let currentQuestionIndex = 0;

    // Initialize category filter dropdown
    function initializeCategoryFilter() {
      const categoryFilter = document.getElementById('categoryFilter');
      categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categoryFilter.appendChild(option);
      });
    }

    // Update dictionary stats and progress
    function updateDictionaryStats() {
      const totalWords = dictionary.length;
      const foundWords = filteredDictionary.length;
      const totalCategories = categories.length;

      document.getElementById('dictionaryStats').textContent =
        `${foundWords} words found | ${totalWords} total words in ${totalCategories} categories`;

      const progressPercentage = (foundWords / totalWords) * 100;
      document.getElementById('progressBar').style.width = `${progressPercentage}%`;
    }

    // Filter dictionary by search and category
    function filterDictionary() {
      const searchQuery = document.getElementById('searchInput').value.toLowerCase();
      const selectedCategory = document.getElementById('categoryFilter').value;

      filteredDictionary = dictionary.filter(item => {
        const matchesSearch = !searchQuery ||
          item.word.toLowerCase().includes(searchQuery) ||
          item.pronunciation.toLowerCase().includes(searchQuery) ||
          item.english.toLowerCase().includes(searchQuery) ||
          item.example.toLowerCase().includes(searchQuery);

        const matchesCategory = !selectedCategory || item.category === selectedCategory;

        return matchesSearch && matchesCategory;
      });

      updateDictionaryStats();
      displayDictionaryResults();
    }

    // Display dictionary results
    function displayDictionaryResults() {
      const dictionaryResults = document.getElementById('dictionaryResults');
      dictionaryResults.innerHTML = '';

      filteredDictionary.forEach(item => {
        const div = document.createElement('div');
        div.className = 'border p-4 bg-white rounded shadow';
        div.innerHTML = `
          <p><strong>${item.word}</strong> ${item.article ? `(${item.article})` : ''} ${item.pronunciation} - ${item.english}</p>
          <p class="text-gray-600 mt-1"><strong>German:</strong> ${item.example}</p>
          ${item.exampleEnglish ? `<p class="text-gray-500 mt-1"><strong>English:</strong> ${item.exampleEnglish}</p>` : ''}
          <div class="mt-2">
            <button class="bg-green-500 text-white px-2 py-1 rounded mr-2" onclick="speakText('${item.word}', 'de-DE')">🔊 German</button>
            <button class="bg-blue-500 text-white px-2 py-1 rounded mr-2" onclick="speakText('${item.english}', 'en-US')">🔊 English</button>
            <button class="bg-purple-500 text-white px-2 py-1 rounded" onclick="speakText('${item.example}', 'de-DE')">🔊 Example</button>
          </div>
        `;
        dictionaryResults.appendChild(div);
      });
    }

    const characters = ['ä', 'ö', 'ü', 'ß', 'A', 'B', 'C', 'D', 'E', 'F'];
    let currentCharIndex = 0;

    // Pronunciation function with fallback
    function speakText(text, lang) {
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = lang;
        utterance.rate = 0.9;
        utterance.onerror = () => console.error('Speech synthesis failed');
        speechSynthesis.speak(utterance);
      } else {
        console.warn('Speech synthesis not supported, attempting audio fallback');
        const item = dictionary.find(item => item.word === text || item.english === text);
        if (item && item.audio) {
          const audio = new Audio(item.audio);
          audio.play().catch(e => console.error('Audio playback failed', e));
        }
      }
    }

    // Enhanced Flashcard functionality
    function updateFlashcard() {
      const item = dictionary[currentFlashcardIndex];
      document.getElementById('flashcardWord').textContent = item.word;
      document.getElementById('flashcardPronunciation').textContent = item.pronunciation;
      document.getElementById('flashcardTranslation').textContent = '(Tap to see translation)';
      document.getElementById('flashcardEnglish').textContent = item.english;
      document.getElementById('flashcardEnglish').classList.add('hidden');
      document.getElementById('flashcardExample').textContent = item.example;

      // Update example English translation if available
      const exampleEnglishElement = document.getElementById('flashcardExampleEnglish');
      if (item.exampleEnglish) {
        exampleEnglishElement.textContent = item.exampleEnglish;
        exampleEnglishElement.style.display = 'block';
      } else {
        exampleEnglishElement.style.display = 'none';
      }

      // Update progress
      document.getElementById('flashcardProgress').textContent = `Card ${currentFlashcardIndex + 1} of ${dictionary.length}`;
      const progressPercentage = ((currentFlashcardIndex + 1) / dictionary.length) * 100;
      document.getElementById('flashcardProgressBar').style.width = `${progressPercentage}%`;
    }

    function toggleTranslation() {
      const translation = document.getElementById('flashcardEnglish');
      translation.classList.toggle('hidden');
    }

    // Writing Practice
    const canvas = document.getElementById('writingCanvas');
    const ctx = canvas.getContext('2d');
    let isDrawing = false;

    canvas.addEventListener('mousedown', () => isDrawing = true);
    canvas.addEventListener('mouseup', () => isDrawing = false);
    canvas.addEventListener('mousemove', (e) => {
      if (isDrawing) {
        const rect = canvas.getBoundingClientRect();
        ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
        ctx.stroke();
      }
    });
    canvas.addEventListener('touchstart', () => isDrawing = true);
    canvas.addEventListener('touchend', () => isDrawing = false);
    canvas.addEventListener('touchmove', (e) => {
      if (isDrawing) {
        const rect = canvas.getBoundingClientRect();
        const touch = e.touches[0];
        ctx.lineTo(touch.clientX - rect.left, touch.clientY - rect.top);
        ctx.stroke();
      }
    });

    document.getElementById('clearCanvas').addEventListener('click', () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.beginPath();
    });

    document.getElementById('nextChar').addEventListener('click', () => {
      currentCharIndex = (currentCharIndex + 1) % characters.length;
      document.getElementById('currentChar').textContent = characters[currentCharIndex];
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.beginPath();
    });

    // Flashcard event listeners
    document.getElementById('prevFlashcard').addEventListener('click', () => {
      currentFlashcardIndex = (currentFlashcardIndex - 1 + dictionary.length) % dictionary.length;
      updateFlashcard();
    });

    document.getElementById('nextFlashcard').addEventListener('click', () => {
      currentFlashcardIndex = (currentFlashcardIndex + 1) % dictionary.length;
      updateFlashcard();
    });

    document.getElementById('speakGerman').addEventListener('click', () => {
      speakText(dictionary[currentFlashcardIndex].word, 'de-DE');
    });

    document.getElementById('speakEnglish').addEventListener('click', () => {
      speakText(dictionary[currentFlashcardIndex].english, 'en-US');
    });

    // Quiz functionality
    function generateQuiz() {
      quizQuestions = [];
      quizScore = 0;
      currentQuestionIndex = 0;

      // Select 10 random words for the quiz
      const shuffledDictionary = [...dictionary].sort(() => Math.random() - 0.5);
      const selectedWords = shuffledDictionary.slice(0, 10);

      selectedWords.forEach(word => {
        // Generate wrong answers from other words
        const wrongAnswers = dictionary
          .filter(item => item.english !== word.english)
          .sort(() => Math.random() - 0.5)
          .slice(0, 3)
          .map(item => item.english);

        const allAnswers = [word.english, ...wrongAnswers].sort(() => Math.random() - 0.5);

        quizQuestions.push({
          question: `What does "${word.word}" mean in English?`,
          correctAnswer: word.english,
          options: allAnswers,
          word: word
        });
      });

      showQuizQuestion();
    }

    function showQuizQuestion() {
      if (currentQuestionIndex >= quizQuestions.length) {
        showQuizResults();
        return;
      }

      const question = quizQuestions[currentQuestionIndex];
      document.getElementById('quizQuestion').textContent = question.question;
      document.getElementById('quizProgress').textContent = `Question ${currentQuestionIndex + 1} of ${quizQuestions.length}`;

      const progressPercentage = ((currentQuestionIndex + 1) / quizQuestions.length) * 100;
      document.getElementById('quizProgressBar').style.width = `${progressPercentage}%`;

      const optionsContainer = document.getElementById('quizOptions');
      optionsContainer.innerHTML = '';

      question.options.forEach((option, index) => {
        const button = document.createElement('button');
        button.className = 'w-full text-left p-3 border rounded hover:bg-gray-100 quiz-option';
        button.textContent = option;
        button.onclick = () => selectQuizAnswer(option, button);
        optionsContainer.appendChild(button);
      });

      document.getElementById('submitAnswer').classList.remove('hidden');
      document.getElementById('nextQuestion').classList.add('hidden');
      document.getElementById('quizFeedback').textContent = '';
    }

    // Quiz event listeners
    document.getElementById('submitAnswer').addEventListener('click', submitQuizAnswer);
    document.getElementById('nextQuestion').addEventListener('click', nextQuizQuestion);
    document.getElementById('startQuiz').addEventListener('click', generateQuiz);

    let selectedQuizAnswer = null;

    function selectQuizAnswer(answer, button) {
      // Remove previous selection
      document.querySelectorAll('.quiz-option').forEach(btn => {
        btn.classList.remove('bg-blue-200');
      });

      // Mark current selection
      button.classList.add('bg-blue-200');
      selectedQuizAnswer = answer;
    }

    function submitQuizAnswer() {
      if (!selectedQuizAnswer) {
        alert('Please select an answer first!');
        return;
      }

      const question = quizQuestions[currentQuestionIndex];
      const isCorrect = selectedQuizAnswer === question.correctAnswer;

      if (isCorrect) {
        quizScore++;
        document.getElementById('quizFeedback').innerHTML = `<span class="text-green-600 font-bold">✓ Correct!</span>`;
      } else {
        document.getElementById('quizFeedback').innerHTML = `<span class="text-red-600 font-bold">✗ Incorrect. The correct answer is: ${question.correctAnswer}</span>`;
      }

      // Speak the German word
      speakText(question.word.word, 'de-DE');

      document.getElementById('submitAnswer').classList.add('hidden');
      document.getElementById('nextQuestion').classList.remove('hidden');
      selectedQuizAnswer = null;
    }

    function nextQuizQuestion() {
      currentQuestionIndex++;
      showQuizQuestion();
    }

    function showQuizResults() {
      const percentage = Math.round((quizScore / quizQuestions.length) * 100);
      document.getElementById('quizScore').textContent = `Quiz completed! Score: ${quizScore}/${quizQuestions.length} (${percentage}%)`;

      // Hide quiz elements
      document.getElementById('quizQuestion').textContent = 'Quiz completed!';
      document.getElementById('quizOptions').innerHTML = '';
      document.getElementById('submitAnswer').classList.add('hidden');
      document.getElementById('nextQuestion').classList.add('hidden');
      document.getElementById('quizFeedback').textContent = '';
    }

    // Sentence Making
    const sentences = [
      { english: "I eat an apple.", german: "Ich esse einen Apfel.", parts: ["Ich", "esse", "einen", "Apfel"] },
      { english: "I read a book.", german: "Ich lese ein Buch.", parts: ["Ich", "lese", "ein", "Buch"] },
      { english: "The dog runs.", german: "Der Hund läuft.", parts: ["Der", "Hund", "läuft"] },
      { english: "The sun shines.", german: "Die Sonne scheint.", parts: ["Die", "Sonne", "scheint"] },
      // Add more sentences using dictionary words
    ];

    let currentSentenceIndex = 0;
    let selectedWords = [];

    function setupSentenceMaking() {
      const sentence = sentences[currentSentenceIndex];
      document.getElementById('sentencePrompt').textContent = `Make a sentence: "${sentence.english}"`;
      const wordBank = document.getElementById('wordBank');
      wordBank.innerHTML = '';
      const shuffledParts = [...sentence.parts, "der", "und"].sort(() => Math.random() - 0.5);
      shuffledParts.forEach(part => {
        const div = document.createElement('div');
        div.textContent = part;
        div.className = 'border p-2 bg-gray-200 cursor-move inline-block mr-2 mb-2';
        div.draggable = true;
        div.addEventListener('dragstart', (e) => e.dataTransfer.setData('text', part));
        div.onclick = () => selectWordForSentence(part, div);
        wordBank.appendChild(div);
      });

      const sentenceArea = document.getElementById('sentenceArea');
      sentenceArea.innerHTML = '';
      selectedWords = [];
      sentenceArea.addEventListener('dragover', (e) => e.preventDefault());
      sentenceArea.addEventListener('drop', (e) => {
        e.preventDefault();
        const word = e.dataTransfer.getData('text');
        addWordToSentence(word);
      });
    }

    function selectWordForSentence(word, element) {
      selectedWords.push(word);
      element.style.display = 'none';
      updateSentenceDisplay();
    }

    function addWordToSentence(word) {
      selectedWords.push(word);
      updateSentenceDisplay();
    }

    function updateSentenceDisplay() {
      const sentenceArea = document.getElementById('sentenceArea');
      sentenceArea.innerHTML = selectedWords.map(word =>
        `<span class="border p-2 bg-gray-300 inline-block mr-2 mb-2">${word}</span>`
      ).join('');
    }

    document.getElementById('checkSentence').addEventListener('click', () => {
      const userSentence = selectedWords.join(' ');
      const correctSentence = sentences[currentSentenceIndex].german;
      const feedback = document.getElementById('sentenceFeedback');
      if (userSentence === correctSentence) {
        feedback.textContent = 'Correct! Great job!';
        feedback.className = 'text-green-500 font-bold';
        currentSentenceIndex = (currentSentenceIndex + 1) % sentences.length;
        setTimeout(setupSentenceMaking, 1000);
      } else {
        feedback.textContent = 'Try again! The correct sentence is: ' + correctSentence;
        feedback.className = 'text-red-500 font-bold';
      }
    });

    document.getElementById('speakSentence').addEventListener('click', () => {
      const userSentence = selectedWords.join(' ');
      if (userSentence) {
        speakText(userSentence, 'de-DE');
      } else {
        speakText(sentences[currentSentenceIndex].german, 'de-DE');
      }
    });

    // Navigation
    const sections = ['dictionary', 'writing', 'flashcards', 'sentence', 'quiz'];

    function showSection(sectionName) {
      sections.forEach(section => {
        document.getElementById(section).classList.add('hidden');
      });
      document.getElementById(sectionName).classList.remove('hidden');
    }

    document.querySelectorAll('nav a').forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const sectionName = link.getAttribute('href').substring(1);
        showSection(sectionName);
        if (sectionName === 'flashcards') updateFlashcard();
        if (sectionName === 'sentence') setupSentenceMaking();
        if (sectionName === 'quiz') generateQuiz();
      });
    });

    // Search and filter event listeners
    document.getElementById('searchInput').addEventListener('input', filterDictionary);
    document.getElementById('categoryFilter').addEventListener('change', filterDictionary);

    // Initialize app
    document.addEventListener('DOMContentLoaded', () => {
      initializeCategoryFilter();
      updateDictionaryStats();
      displayDictionaryResults();
      updateFlashcard();
      setupSentenceMaking();
    });
  </script>
</body>
</html>