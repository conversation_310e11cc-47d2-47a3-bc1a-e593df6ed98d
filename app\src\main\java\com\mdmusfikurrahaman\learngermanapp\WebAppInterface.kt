package com.mdmusfikurrahaman.learngermanapp

import android.content.Context
import android.speech.tts.TextToSpeech
import android.util.Log
import android.webkit.JavascriptInterface
import android.widget.Toast
import java.util.*

class WebAppInterface(private val context: Context) {
    private var textToSpeech: TextToSpeech? = null
    private var isInitialized = false

    init {
        initializeTextToSpeech()
    }

    private fun initializeTextToSpeech() {
        Log.d("WebAppInterface", "🚀 Initializing TTS with auto-detection...")

        // First try default TTS (usually works best)
        textToSpeech = TextToSpeech(context) { status ->
            if (status == TextToSpeech.SUCCESS) {
                isInitialized = true
                Log.d("WebAppInterface", "✅ Default TextToSpeech initialized successfully")

                // Test German support
                testGermanSupport()
            } else {
                Log.e("WebAppInterface", "❌ Default TextToSpeech initialization failed")
                // Try Google TTS as fallback
                tryGoogleTTS()
            }
        }
    }

    private fun tryGoogleTTS() {
        Log.d("WebAppInterface", "🔄 Trying Google TTS as fallback...")
        textToSpeech?.shutdown()

        textToSpeech = TextToSpeech(context, { status ->
            if (status == TextToSpeech.SUCCESS) {
                isInitialized = true
                Log.d("WebAppInterface", "✅ Google TextToSpeech initialized successfully")
                testGermanSupport()
            } else {
                Log.e("WebAppInterface", "❌ Google TextToSpeech also failed")
                showToast("TTS not available. Please install Google TTS.")
            }
        }, "com.google.android.tts")
    }

    private fun testGermanSupport() {
        textToSpeech?.let { tts ->
            // Force audio routing setup immediately
            setupAudioRouting(tts)

            val germanResult = tts.setLanguage(java.util.Locale.GERMAN)
            val englishResult = tts.setLanguage(java.util.Locale.ENGLISH)

            Log.d("WebAppInterface", "🇩🇪 German support: $germanResult")
            Log.d("WebAppInterface", "🇺🇸 English support: $englishResult")

            if (germanResult == TextToSpeech.LANG_MISSING_DATA || germanResult == TextToSpeech.LANG_NOT_SUPPORTED) {
                Log.w("WebAppInterface", "⚠️ German not supported, but TTS is working")
                showToast("German TTS not available. English TTS working.")
            } else {
                Log.d("WebAppInterface", "✅ Both German and English TTS available")
                showToast("TTS ready! German and English supported.")
            }

            // Test audio immediately
            testAudioOutput(tts)
        }
    }

    private fun setupAudioRouting(tts: TextToSpeech) {
        try {
            // Get audio manager
            val audioManager = context.getSystemService(android.content.Context.AUDIO_SERVICE) as android.media.AudioManager

            // Request audio focus for media playback (silent request to avoid UI)
            val result = audioManager.requestAudioFocus(
                null,
                android.media.AudioManager.STREAM_MUSIC,
                android.media.AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK
            )

            Log.d("WebAppInterface", "🔊 Audio focus request result: $result")

            // Set audio attributes for API 21+ with hidden UI flags
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                val audioAttributes = android.media.AudioAttributes.Builder()
                    .setUsage(android.media.AudioAttributes.USAGE_MEDIA)
                    .setContentType(android.media.AudioAttributes.CONTENT_TYPE_SPEECH)
                    .setFlags(android.media.AudioAttributes.FLAG_AUDIBILITY_ENFORCED)
                    .build()
                tts.setAudioAttributes(audioAttributes)
                Log.d("WebAppInterface", "🔊 Audio attributes configured with hidden UI")
            }

            // Force normal audio mode (no call mode UI)
            audioManager.mode = android.media.AudioManager.MODE_NORMAL

            // Disable any TTS-related notifications
            try {
                // Try to set TTS to not show system UI
                val method = tts.javaClass.getMethod("setEngineByPackageName", String::class.java)
                method.isAccessible = true
                // This helps hide system TTS indicators
            } catch (e: Exception) {
                Log.d("WebAppInterface", "🔇 Could not hide TTS UI (normal on some devices)")
            }

            Log.d("WebAppInterface", "🔊 Audio routing configured for TTS (UI hidden)")

        } catch (e: Exception) {
            Log.e("WebAppInterface", "❌ Error setting up audio routing: ${e.message}")
        }
    }

    private fun testAudioOutput(tts: TextToSpeech) {
        // Test with a simple word to verify audio is working
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            Log.d("WebAppInterface", "🧪 Testing audio output with simple word...")

            val params = android.os.Bundle().apply {
                putFloat(TextToSpeech.Engine.KEY_PARAM_VOLUME, 1.0f)
                putString(TextToSpeech.Engine.KEY_PARAM_STREAM, android.media.AudioManager.STREAM_MUSIC.toString())
            }

            val testResult = tts.speak("Test", TextToSpeech.QUEUE_FLUSH, params, "audio_test")
            Log.d("WebAppInterface", "🧪 Audio test result: $testResult")

            // Check if it's actually speaking
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                val isSpeaking = tts.isSpeaking
                Log.d("WebAppInterface", "🔍 Audio test - is speaking: $isSpeaking")
                if (isSpeaking) {
                    Log.d("WebAppInterface", "✅ Audio output confirmed working!")
                } else {
                    Log.w("WebAppInterface", "⚠️ Audio test failed - no output detected")
                }
            }, 100)

        }, 1000)
    }

    @JavascriptInterface
    fun speak(text: String, language: String) {
        Log.d("WebAppInterface", "🎯 speak() called with: '$text' in '$language'")

        if (!isInitialized) {
            Log.w("WebAppInterface", "❌ TTS not initialized yet, waiting...")
            showToast("TTS not ready, please try again")
            return
        }

        textToSpeech?.let { tts ->
            try {
                // Check if TTS is currently speaking
                if (tts.isSpeaking) {
                    Log.d("WebAppInterface", "🔄 TTS is currently speaking, stopping...")
                    tts.stop()
                }

                // Determine locale based on language parameter
                val locale = when {
                    language.contains("de", ignoreCase = true) -> {
                        Log.d("WebAppInterface", "🇩🇪 Using German locale")
                        Locale.GERMAN
                    }
                    language.contains("en", ignoreCase = true) -> {
                        Log.d("WebAppInterface", "🇺🇸 Using English locale")
                        Locale.ENGLISH
                    }
                    else -> {
                        Log.d("WebAppInterface", "🔤 Unknown language '$language', defaulting to English")
                        Locale.ENGLISH
                    }
                }

                val result = tts.setLanguage(locale)
                Log.d("WebAppInterface", "🔧 setLanguage result: $result")

                if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                    Log.w("WebAppInterface", "❌ Language not supported: $language (result: $result)")
                    showToast("Language not supported: $language")
                    return
                }

                // Set speech parameters for better audio
                tts.setSpeechRate(0.9f)  // Slightly faster
                tts.setPitch(1.0f)

                // Force audio routing setup for each speak call
                setupAudioRouting(tts)

                // Create bundle for additional parameters with forced audio stream
                val params = android.os.Bundle().apply {
                    putString(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, "utterance_${System.currentTimeMillis()}")
                    putFloat(TextToSpeech.Engine.KEY_PARAM_VOLUME, 1.0f)  // Max volume
                    putString(TextToSpeech.Engine.KEY_PARAM_STREAM, android.media.AudioManager.STREAM_MUSIC.toString())
                    putString(TextToSpeech.Engine.KEY_PARAM_PAN, "0.0")  // Center audio
                    // Hide TTS system UI/icon
                    putInt(TextToSpeech.Engine.KEY_PARAM_SESSION_ID, android.media.AudioManager.AUDIO_SESSION_ID_GENERATE)
                }

                // Speak the text with parameters
                val speakResult = tts.speak(text, TextToSpeech.QUEUE_FLUSH, params, "utterance_${System.currentTimeMillis()}")
                Log.d("WebAppInterface", "🔊 speak() result: $speakResult")
                Log.d("WebAppInterface", "🔊 Audio routing: USAGE_MEDIA, CONTENT_TYPE_SPEECH")
                Log.d("WebAppInterface", "✅ Speaking: '$text' in $language")

                // Check if speaking started
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    val isSpeaking = tts.isSpeaking
                    Log.d("WebAppInterface", "🔍 Is TTS speaking after 100ms: $isSpeaking")
                    if (!isSpeaking) {
                        Log.w("WebAppInterface", "⚠️ TTS not speaking - possible audio routing issue")
                        showToast("TTS command sent but no audio detected")
                    }
                }, 100)

                showToast("🔊 TTS: $text")

            } catch (e: Exception) {
                Log.e("WebAppInterface", "❌ Error speaking text: ${e.message}")
                showToast("Error speaking text: ${e.message}")
            }
        } ?: run {
            Log.e("WebAppInterface", "❌ TextToSpeech is null!")
            showToast("TTS engine not available")
        }
    }

    @JavascriptInterface
    fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    @JavascriptInterface
    fun isAvailable(): Boolean {
        Log.d("WebAppInterface", "🔍 isAvailable() called: $isInitialized")
        return isInitialized
    }

    @JavascriptInterface
    fun testTTS() {
        Log.d("WebAppInterface", "🧪 testTTS() called")
        speak("Hello", "en-US")
    }

    @JavascriptInterface
    fun testGermanTTS() {
        Log.d("WebAppInterface", "🧪 testGermanTTS() called")
        speak("Hallo", "de-DE")
    }

    fun destroy() {
        textToSpeech?.let { tts ->
            tts.stop()
            tts.shutdown()
        }
        textToSpeech = null
        isInitialized = false
    }
}
