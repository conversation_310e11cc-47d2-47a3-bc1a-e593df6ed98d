<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 25px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 200px;
        }
        button:hover {
            background: #45a049;
        }
        .status {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #2196F3;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 TTS Debug Tool</h1>
        
        <div class="status">
            <strong>Debug Information:</strong><br>
            This page helps debug TTS issues by testing different methods.
        </div>

        <h2>🧪 Direct Android TTS Tests</h2>
        <button onclick="testAndroidEnglish()">Test Android English TTS</button>
        <button onclick="testAndroidGerman()">Test Android German TTS</button>
        <button onclick="checkAndroidTTS()">Check Android TTS Status</button>

        <h2>🌐 Web speechSynthesis Tests</h2>
        <button onclick="testWebEnglish()">Test Web English TTS</button>
        <button onclick="testWebGerman()">Test Web German TTS</button>
        <button onclick="checkWebTTS()">Check Web TTS Status</button>

        <h2>📱 Your App's speakText Function</h2>
        <button onclick="testAppEnglish()">Test App English TTS</button>
        <button onclick="testAppGerman()">Test App German TTS</button>

        <div id="log" class="log">
            <strong>Debug Log:</strong><br>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // Direct Android TTS tests
        function testAndroidEnglish() {
            log('🧪 Testing Android English TTS...');
            if (typeof AndroidTTS !== 'undefined') {
                AndroidTTS.testTTS();
                log('✅ Android English TTS test called');
            } else {
                log('❌ AndroidTTS not available');
            }
        }

        function testAndroidGerman() {
            log('🧪 Testing Android German TTS...');
            if (typeof AndroidTTS !== 'undefined') {
                AndroidTTS.testGermanTTS();
                log('✅ Android German TTS test called');
            } else {
                log('❌ AndroidTTS not available');
            }
        }

        function checkAndroidTTS() {
            log('🔍 Checking Android TTS status...');
            if (typeof AndroidTTS !== 'undefined') {
                const available = AndroidTTS.isAvailable();
                log('Android TTS available: ' + available);
            } else {
                log('❌ AndroidTTS interface not found');
            }
        }

        // Web speechSynthesis tests
        function testWebEnglish() {
            log('🌐 Testing Web English TTS...');
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance('Hello World');
                utterance.lang = 'en-US';
                utterance.onstart = () => log('✅ Web English TTS started');
                utterance.onend = () => log('✅ Web English TTS ended');
                utterance.onerror = (e) => log('❌ Web English TTS error: ' + e.error);
                speechSynthesis.speak(utterance);
                log('Web English TTS command sent');
            } else {
                log('❌ speechSynthesis not available');
            }
        }

        function testWebGerman() {
            log('🌐 Testing Web German TTS...');
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance('Hallo Welt');
                utterance.lang = 'de-DE';
                utterance.onstart = () => log('✅ Web German TTS started');
                utterance.onend = () => log('✅ Web German TTS ended');
                utterance.onerror = (e) => log('❌ Web German TTS error: ' + e.error);
                speechSynthesis.speak(utterance);
                log('Web German TTS command sent');
            } else {
                log('❌ speechSynthesis not available');
            }
        }

        function checkWebTTS() {
            log('🔍 Checking Web TTS status...');
            log('speechSynthesis available: ' + (typeof speechSynthesis !== 'undefined'));
            log('SpeechSynthesisUtterance available: ' + (typeof SpeechSynthesisUtterance !== 'undefined'));
            
            if ('speechSynthesis' in window) {
                const voices = speechSynthesis.getVoices();
                log('Total voices: ' + voices.length);
                const germanVoices = voices.filter(v => v.lang.startsWith('de'));
                log('German voices: ' + germanVoices.length);
            }
        }

        // App's speakText function tests
        function testAppEnglish() {
            log('📱 Testing App English TTS...');
            if (typeof speakText === 'function') {
                speakText('Hello from app', 'en-US');
                log('App English TTS called');
            } else {
                log('❌ speakText function not found');
            }
        }

        function testAppGerman() {
            log('📱 Testing App German TTS...');
            if (typeof speakText === 'function') {
                speakText('Hallo von der App', 'de-DE');
                log('App German TTS called');
            } else {
                log('❌ speakText function not found');
            }
        }

        // Initial status check
        window.addEventListener('load', function() {
            log('🚀 TTS Debug Tool loaded');
            setTimeout(function() {
                checkAndroidTTS();
                checkWebTTS();
            }, 1000);
        });
    </script>
</body>
</html>
