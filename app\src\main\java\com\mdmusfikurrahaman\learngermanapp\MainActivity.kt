package com.mdmusfikurrahaman.learngermanapp

import android.annotation.SuppressLint
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ProgressBar
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import com.google.android.gms.ads.AdView
import com.mdmusfikurrahaman.learngermanapp.databinding.ActivityMainBinding

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var webAppInterface: WebAppInterface
    private lateinit var adManager: AdManager

    companion object {
        private const val TAG = "MainActivity"
        private const val WEB_APP_URL = "file:///android_asset/index.html"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize components
        webAppInterface = WebAppInterface(this)
        adManager = AdManager(this)

        // Setup WebView
        setupWebView()

        // Initialize and load ads
        initializeAds()

        // Load the web app
        loadWebApp()

        // Setup modern back press handling
        setupBackPressHandling()
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun setupWebView() {
        // Enable remote debugging for WebView (helps with speechSynthesis)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true)
        }

        binding.webView.apply {
            // Enable JavaScript
            settings.javaScriptEnabled = true

            // Enable DOM storage
            settings.domStorageEnabled = true

            // Enable local storage
            settings.databaseEnabled = true

            // App cache is deprecated in modern WebView

            // Allow file access
            settings.allowFileAccess = true
            settings.allowContentAccess = true

            // Enable zoom controls
            settings.setSupportZoom(true)
            settings.builtInZoomControls = true
            settings.displayZoomControls = false

            // Set cache mode
            settings.cacheMode = WebSettings.LOAD_DEFAULT

            // Enable mixed content (if needed)
            settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW

            // Enable media playback and audio
            settings.mediaPlaybackRequiresUserGesture = false

            // Enable hardware acceleration for better performance
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                setLayerType(View.LAYER_TYPE_HARDWARE, null)
            }

            // Additional settings for speechSynthesis support
            settings.allowUniversalAccessFromFileURLs = true
            settings.allowFileAccessFromFileURLs = true

            // Set user agent to latest Chrome to enable speechSynthesis API
            settings.userAgentString = "Mozilla/5.0 (Linux; Android 12; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Mobile Safari/537.36"
        }

        // Add JavaScript interface to provide TTS since speechSynthesis is not available
        binding.webView.addJavascriptInterface(webAppInterface, "AndroidTTS")

        binding.webView.apply {
            // Set WebView client
            webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    binding.progressBar.visibility = View.GONE
                    Log.d(TAG, "Page loaded: $url")

                    // Inject speechSynthesis polyfill using Android TTS
                    injectSpeechSynthesisPolyfill()
                }

                override fun onReceivedError(view: WebView?, errorCode: Int, description: String?, failingUrl: String?) {
                    super.onReceivedError(view, errorCode, description, failingUrl)
                    Log.e(TAG, "WebView error: $description")
                    binding.progressBar.visibility = View.GONE
                }
            }

            // Set WebChrome client for progress and console logging
            webChromeClient = object : WebChromeClient() {
                override fun onProgressChanged(view: WebView?, newProgress: Int) {
                    super.onProgressChanged(view, newProgress)
                    if (newProgress < 100) {
                        binding.progressBar.visibility = View.VISIBLE
                    } else {
                        binding.progressBar.visibility = View.GONE
                    }
                }

                override fun onConsoleMessage(consoleMessage: android.webkit.ConsoleMessage?): Boolean {
                    consoleMessage?.let {
                        Log.d("WebView-Console", "${it.messageLevel()}: ${it.message()} -- From line ${it.lineNumber()} of ${it.sourceId()}")
                    }
                    return true
                }
            }
        }
    }

    private fun injectSpeechSynthesisPolyfill() {
        val jsCode = """
            console.log('=== Injecting speechSynthesis Polyfill ===');

            // Check if speechSynthesis already exists
            console.log('🔧 FORCING speechSynthesis polyfill override...');
            console.log('🔍 Original speechSynthesis type:', typeof speechSynthesis);

            // ALWAYS override speechSynthesis to ensure our polyfill works
            if (typeof speechSynthesis !== 'undefined') {
                console.log('⚠️ speechSynthesis exists but forcing override for Android TTS');
            } else {
                console.log('❌ speechSynthesis not available - creating Android TTS polyfill');
            }

            // Create SpeechSynthesisUtterance constructor
            window.SpeechSynthesisUtterance = function(text) {
                this.text = text || '';
                this.lang = 'en-US';
                this.rate = 1;
                this.pitch = 1;
                this.volume = 1;
                this.onstart = null;
                this.onend = null;
                this.onerror = null;
            };

            // Create speechSynthesis object
            window.speechSynthesis = {
                speak: function(utterance) {
                    console.log('🔊🔊🔊 POLYFILL SPEAK CALLED! 🔊🔊🔊');
                    console.log('🎯 Text: "' + utterance.text + '"');
                    console.log('🎯 Language: ' + utterance.lang);
                    console.log('🎯 AndroidTTS type:', typeof AndroidTTS);

                    if (typeof AndroidTTS !== 'undefined') {
                        console.log('✅ AndroidTTS is available');
                        console.log('🔍 AndroidTTS.isAvailable():', AndroidTTS.isAvailable());

                        if (AndroidTTS.isAvailable()) {
                            console.log('✅ AndroidTTS is ready, calling speak...');

                            // Call onstart if defined
                            if (typeof utterance.onstart === 'function') {
                                console.log('🔊 Calling onstart event');
                                utterance.onstart();
                            }

                            // Use Android TTS
                            console.log('🔊 Calling AndroidTTS.speak...');
                            AndroidTTS.speak(utterance.text, utterance.lang);
                            console.log('🔊 AndroidTTS.speak called successfully');

                            // Call onend after a delay (simulate speech completion)
                            setTimeout(function() {
                                if (typeof utterance.onend === 'function') {
                                    console.log('🔊 Calling onend event');
                                    utterance.onend();
                                }
                            }, utterance.text.length * 100); // Rough estimate of speech duration
                        } else {
                            console.log('❌ AndroidTTS not ready/available');
                            if (typeof utterance.onerror === 'function') {
                                utterance.onerror({error: 'not-ready'});
                            }
                        }
                    } else {
                        console.log('❌ AndroidTTS is undefined');
                        if (typeof utterance.onerror === 'function') {
                            utterance.onerror({error: 'not-supported'});
                        }
                    }
                },

                cancel: function() {
                    console.log('🔊 speechSynthesis.cancel() called');
                },

                getVoices: function() {
                    console.log('🔊 speechSynthesis.getVoices() called');
                    return []; // Return empty array for now
                },

                speaking: false,
                pending: false,
                paused: false
            };

            console.log('✅ speechSynthesis polyfill created successfully!');

            // Add test functions to window
            window.testEnglishTTS = function() {
                console.log('🧪 Testing English TTS...');
                if (typeof AndroidTTS !== 'undefined') {
                    AndroidTTS.testTTS();
                } else {
                    console.log('❌ AndroidTTS not available');
                }
            };

            window.testGermanTTS = function() {
                console.log('🧪 Testing German TTS...');
                if (typeof AndroidTTS !== 'undefined') {
                    AndroidTTS.testGermanTTS();
                } else {
                    console.log('❌ AndroidTTS not available');
                }
            };

            window.openTTSTest = function() {
                console.log('🔧 Opening TTS Test Activity...');
                if (typeof AndroidTTS !== 'undefined') {
                    AndroidTTS.showToast('Opening TTS Test...');
                }
            };

            // Test the polyfill automatically with working audio
            setTimeout(function() {
                console.log('🧪 Auto-testing speechSynthesis polyfill with working audio...');
                if (typeof speechSynthesis !== 'undefined' && typeof AndroidTTS !== 'undefined') {
                    console.log('✅ Both speechSynthesis and AndroidTTS available, testing...');

                    // Test English
                    const testUtterance = new SpeechSynthesisUtterance('Hello World');
                    testUtterance.lang = 'en-US';
                    testUtterance.onstart = function() {
                        console.log('✅ Polyfill test: onstart fired');
                    };
                    testUtterance.onend = function() {
                        console.log('✅ Polyfill test: onend fired');
                    };
                    testUtterance.onerror = function(event) {
                        console.log('❌ Polyfill test error:', event.error);
                    };
                    speechSynthesis.speak(testUtterance);
                    console.log('🔊 Auto-test English TTS command sent');
                } else {
                    console.log('❌ speechSynthesis or AndroidTTS not available');
                    console.log('speechSynthesis available:', typeof speechSynthesis !== 'undefined');
                    console.log('AndroidTTS available:', typeof AndroidTTS !== 'undefined');
                }
            }, 4000);

            // Test German TTS
            setTimeout(function() {
                console.log('🧪 Auto-testing German TTS...');
                if (typeof speechSynthesis !== 'undefined') {
                    const germanUtterance = new SpeechSynthesisUtterance('Hallo Welt');
                    germanUtterance.lang = 'de-DE';
                    germanUtterance.onstart = function() {
                        console.log('✅ German test: onstart fired');
                    };
                    germanUtterance.onend = function() {
                        console.log('✅ German test: onend fired');
                    };
                    germanUtterance.onerror = function(event) {
                        console.log('❌ German test error:', event.error);
                    };
                    speechSynthesis.speak(germanUtterance);
                    console.log('🔊 German auto-test TTS command sent');
                } else {
                    console.log('❌ speechSynthesis not available for German test');
                }
            }, 6000);

            // Test direct AndroidTTS calls
            setTimeout(function() {
                console.log('🧪 Testing direct AndroidTTS calls...');
                if (typeof AndroidTTS !== 'undefined') {
                    console.log('🔊 Testing direct English TTS...');
                    AndroidTTS.speak('Direct English test', 'en-US');

                    setTimeout(function() {
                        console.log('🔊 Testing direct German TTS...');
                        AndroidTTS.speak('Direkter deutscher Test', 'de-DE');
                    }, 2000);
                } else {
                    console.log('❌ AndroidTTS not available for direct test');
                }
            }, 8000);

            console.log('=== Polyfill Injection Complete ===');
        """.trimIndent()

        binding.webView.evaluateJavascript(jsCode) { result ->
            Log.d(TAG, "🚀 FINAL WORKING VERSION (23:05) - speechSynthesis polyfill injected: $result")
        }
    }

    private fun initializeAds() {
        Log.d(TAG, "🎯 Initializing AdMob with App ID: ca-app-pub-2281902770675036~8656192515")
        adManager.initializeAds {
            Log.d(TAG, "✅ AdMob initialization complete")
            Log.d(TAG, "🎯 Loading banner ad with Unit ID: ca-app-pub-2281902770675036/1560909931")
            // Use release mode (false) to load real ads, not test ads
            adManager.loadBannerAd(binding.adView, isDebug = false)
        }
    }

    private fun loadWebApp() {
        binding.progressBar.visibility = View.VISIBLE
        binding.webView.loadUrl(WEB_APP_URL)
        Log.d(TAG, "Loading web app from: $WEB_APP_URL")
    }

    private fun setupBackPressHandling() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (binding.webView.canGoBack()) {
                    binding.webView.goBack()
                } else {
                    finish()
                }
            }
        })
    }

    override fun onPause() {
        super.onPause()
        binding.webView.onPause()
        adManager.pauseAd(binding.adView)
    }

    override fun onResume() {
        super.onResume()
        binding.webView.onResume()
        adManager.resumeAd(binding.adView)
    }

    override fun onDestroy() {
        super.onDestroy()
        webAppInterface.destroy()
        adManager.destroyAd(binding.adView)
        binding.webView.destroy()
    }
}