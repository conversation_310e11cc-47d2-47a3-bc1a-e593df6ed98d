package com.mdmusfikurrahaman.learngermanapp

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.speech.tts.TextToSpeech
import android.util.Log
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import java.util.*

class TTSTestActivity : AppCompatActivity(), TextToSpeech.OnInitListener {
    
    private var textToSpeech: TextToSpeech? = null
    private var isInitialized = false
    private lateinit var statusText: TextView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Create simple layout programmatically
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }

        // Title
        val title = TextView(this).apply {
            text = "🎯 TTS Test & Setup"
            textSize = 20f
            setPadding(0, 0, 0, 20)
        }
        layout.addView(title)

        // Status text
        statusText = TextView(this).apply {
            text = "Initializing TTS..."
            textSize = 16f
            setPadding(0, 0, 0, 30)
        }
        layout.addView(statusText)

        // English test button
        val englishButton = Button(this).apply {
            text = "🇺🇸 Test English TTS"
            setOnClickListener { testEnglishTTS() }
        }
        layout.addView(englishButton)

        // German test button
        val germanButton = Button(this).apply {
            text = "🇩🇪 Test German TTS"
            setOnClickListener { testGermanTTS() }
        }
        layout.addView(germanButton)

        // Install Google TTS button
        val installGoogleButton = Button(this).apply {
            text = "📥 Install Google TTS (Recommended)"
            setOnClickListener { installGoogleTTS() }
        }
        layout.addView(installGoogleButton)

        // Open TTS settings button
        val settingsButton = Button(this).apply {
            text = "⚙️ Open TTS Settings"
            setOnClickListener { openTTSSettings() }
        }
        layout.addView(settingsButton)

        // Check TTS status button
        val statusButton = Button(this).apply {
            text = "🔍 Check TTS Status"
            setOnClickListener { checkTTSStatus() }
        }
        layout.addView(statusButton)

        // Test audio routing button
        val audioTestButton = Button(this).apply {
            text = "🔊 Test Audio Routing"
            setOnClickListener { testAudioRouting() }
        }
        layout.addView(audioTestButton)
        
        setContentView(layout)
        
        // Initialize TTS
        initializeTTS()
    }
    
    private fun initializeTTS() {
        Log.d("TTSTest", "🚀 Initializing TextToSpeech with smart detection...")
        // Try default TTS first (works best on most devices)
        textToSpeech = TextToSpeech(this, this)
    }
    
    override fun onInit(status: Int) {
        Log.d("TTSTest", "🔧 onInit called with status: $status")
        
        if (status == TextToSpeech.SUCCESS) {
            isInitialized = true
            statusText.text = "✅ TTS Initialized Successfully!"
            Log.d("TTSTest", "✅ TTS initialized successfully")

            // Test language availability
            checkLanguageSupport()

        } else {
            statusText.text = "❌ TTS Initialization Failed\nTap 'Install Google TTS' for better support"
            Log.e("TTSTest", "❌ TTS initialization failed with status: $status")
            Toast.makeText(this, "TTS initialization failed. Try installing Google TTS.", Toast.LENGTH_LONG).show()
        }
    }
    
    private fun checkLanguageSupport() {
        textToSpeech?.let { tts ->
            // Check English
            val englishResult = tts.setLanguage(Locale.ENGLISH)
            Log.d("TTSTest", "🇺🇸 English support result: $englishResult")
            
            // Check German
            val germanResult = tts.setLanguage(Locale.GERMAN)
            Log.d("TTSTest", "🇩🇪 German support result: $germanResult")
            
            val englishSupported = englishResult != TextToSpeech.LANG_MISSING_DATA && 
                                 englishResult != TextToSpeech.LANG_NOT_SUPPORTED
            val germanSupported = germanResult != TextToSpeech.LANG_MISSING_DATA && 
                                germanResult != TextToSpeech.LANG_NOT_SUPPORTED
            
            statusText.text = "✅ TTS Ready!\n🇺🇸 English: ${if (englishSupported) "✅" else "❌"}\n🇩🇪 German: ${if (germanSupported) "✅" else "❌"}\n\n${if (!germanSupported) "Tap 'Install Google TTS' for German support" else "Ready to use!"}"
        }
    }
    
    private fun testEnglishTTS() {
        Log.d("TTSTest", "🧪 Testing English TTS...")

        if (!isInitialized) {
            Toast.makeText(this, "TTS not initialized", Toast.LENGTH_SHORT).show()
            return
        }

        // Check volume levels
        checkAudioSettings()

        textToSpeech?.let { tts ->
            val result = tts.setLanguage(Locale.ENGLISH)
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                Toast.makeText(this, "English not supported", Toast.LENGTH_SHORT).show()
                Log.w("TTSTest", "❌ English not supported")
                return
            }

            // Set audio attributes for proper routing
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                val audioAttributes = android.media.AudioAttributes.Builder()
                    .setUsage(android.media.AudioAttributes.USAGE_MEDIA)
                    .setContentType(android.media.AudioAttributes.CONTENT_TYPE_SPEECH)
                    .build()
                tts.setAudioAttributes(audioAttributes)
                Log.d("TTSTest", "🔊 Audio attributes set for English TTS")
            }

            tts.setSpeechRate(0.8f)
            tts.setPitch(1.0f)

            // Create bundle with volume parameter
            val params = android.os.Bundle().apply {
                putFloat(TextToSpeech.Engine.KEY_PARAM_VOLUME, 1.0f)
            }

            val speakResult = tts.speak("Hello World, this is English TTS test", TextToSpeech.QUEUE_FLUSH, params, "english_test")
            Log.d("TTSTest", "🔊 English speak result: $speakResult")

            // Check if speaking after delay
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                val isSpeaking = tts.isSpeaking
                Log.d("TTSTest", "🔍 Is English TTS speaking: $isSpeaking")
                if (!isSpeaking) {
                    Toast.makeText(this, "⚠️ TTS command sent but no audio", Toast.LENGTH_LONG).show()
                } else {
                    Toast.makeText(this, "✅ English TTS is speaking!", Toast.LENGTH_SHORT).show()
                }
            }, 200)

            Toast.makeText(this, "🔊 English TTS command sent...", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun testGermanTTS() {
        Log.d("TTSTest", "🧪 Testing German TTS...")
        
        if (!isInitialized) {
            Toast.makeText(this, "TTS not initialized", Toast.LENGTH_SHORT).show()
            return
        }
        
        textToSpeech?.let { tts ->
            val result = tts.setLanguage(Locale.GERMAN)
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                Toast.makeText(this, "German not supported", Toast.LENGTH_SHORT).show()
                Log.w("TTSTest", "❌ German not supported")
                return
            }
            
            tts.setSpeechRate(0.8f)
            tts.setPitch(1.0f)
            
            val speakResult = tts.speak("Hallo Welt, das ist ein deutscher TTS Test", TextToSpeech.QUEUE_FLUSH, null, "german_test")
            Log.d("TTSTest", "🔊 German speak result: $speakResult")
            Toast.makeText(this, "Speaking German...", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun installGoogleTTS() {
        Log.d("TTSTest", "📥 Opening Google TTS in Play Store...")
        try {
            val intent = Intent(Intent.ACTION_VIEW).apply {
                data = Uri.parse("https://play.google.com/store/apps/details?id=com.google.android.tts")
                setPackage("com.android.vending")
            }
            startActivity(intent)
            Toast.makeText(this, "Install Google TTS, then return to test", Toast.LENGTH_LONG).show()
        } catch (e: Exception) {
            Toast.makeText(this, "Please install Google TTS from Play Store", Toast.LENGTH_LONG).show()
        }
    }

    private fun checkAudioSettings() {
        val audioManager = getSystemService(android.content.Context.AUDIO_SERVICE) as android.media.AudioManager

        // Check media volume
        val mediaVolume = audioManager.getStreamVolume(android.media.AudioManager.STREAM_MUSIC)
        val maxMediaVolume = audioManager.getStreamMaxVolume(android.media.AudioManager.STREAM_MUSIC)

        // Check notification volume (sometimes used for TTS)
        val notificationVolume = audioManager.getStreamVolume(android.media.AudioManager.STREAM_NOTIFICATION)
        val maxNotificationVolume = audioManager.getStreamMaxVolume(android.media.AudioManager.STREAM_NOTIFICATION)

        Log.d("TTSTest", "🔊 Media volume: $mediaVolume/$maxMediaVolume")
        Log.d("TTSTest", "🔊 Notification volume: $notificationVolume/$maxNotificationVolume")

        if (mediaVolume == 0 && notificationVolume == 0) {
            Toast.makeText(this, "⚠️ Volume is muted! Please increase volume", Toast.LENGTH_LONG).show()
            Log.w("TTSTest", "⚠️ Both media and notification volumes are muted")
        } else if (mediaVolume < maxMediaVolume * 0.3) {
            Toast.makeText(this, "🔊 Media volume is low, consider increasing it", Toast.LENGTH_SHORT).show()
        }

        // Check if audio focus is available
        val result = audioManager.requestAudioFocus(
            null,
            android.media.AudioManager.STREAM_MUSIC,
            android.media.AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
        )

        if (result == android.media.AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
            Log.d("TTSTest", "✅ Audio focus granted")
            audioManager.abandonAudioFocus(null)
        } else {
            Log.w("TTSTest", "⚠️ Audio focus denied")
        }
    }

    private fun openTTSSettings() {
        Log.d("TTSTest", "⚙️ Opening TTS settings...")
        try {
            val intent = Intent("com.android.settings.TTS_SETTINGS")
            startActivity(intent)
            Toast.makeText(this, "Select Google TTS and download German", Toast.LENGTH_LONG).show()
        } catch (e: Exception) {
            try {
                val intent = Intent(android.provider.Settings.ACTION_ACCESSIBILITY_SETTINGS)
                startActivity(intent)
                Toast.makeText(this, "Go to Text-to-speech settings", Toast.LENGTH_LONG).show()
            } catch (e2: Exception) {
                Toast.makeText(this, "Please go to Settings > Language & Input > Text-to-speech", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun testAudioRouting() {
        Log.d("TTSTest", "🔊 Testing audio routing...")

        if (!isInitialized) {
            Toast.makeText(this, "TTS not initialized", Toast.LENGTH_SHORT).show()
            return
        }

        textToSpeech?.let { tts ->
            // Force audio setup
            val audioManager = getSystemService(android.content.Context.AUDIO_SERVICE) as android.media.AudioManager

            // Request audio focus
            val focusResult = audioManager.requestAudioFocus(
                null,
                android.media.AudioManager.STREAM_MUSIC,
                android.media.AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
            )
            Log.d("TTSTest", "🔊 Audio focus result: $focusResult")

            // Set audio attributes
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                val audioAttributes = android.media.AudioAttributes.Builder()
                    .setUsage(android.media.AudioAttributes.USAGE_MEDIA)
                    .setContentType(android.media.AudioAttributes.CONTENT_TYPE_SPEECH)
                    .setFlags(android.media.AudioAttributes.FLAG_AUDIBILITY_ENFORCED)
                    .build()
                tts.setAudioAttributes(audioAttributes)
                Log.d("TTSTest", "🔊 Audio attributes set")
            }

            // Force normal audio mode
            audioManager.mode = android.media.AudioManager.MODE_NORMAL

            // Test with forced parameters
            val params = android.os.Bundle().apply {
                putFloat(TextToSpeech.Engine.KEY_PARAM_VOLUME, 1.0f)
                putString(TextToSpeech.Engine.KEY_PARAM_STREAM, android.media.AudioManager.STREAM_MUSIC.toString())
            }

            tts.setLanguage(Locale.ENGLISH)
            val result = tts.speak("Audio routing test", TextToSpeech.QUEUE_FLUSH, params, "audio_routing_test")
            Log.d("TTSTest", "🔊 Audio routing test result: $result")

            // Check if speaking
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                val isSpeaking = tts.isSpeaking
                Log.d("TTSTest", "🔍 Audio routing test - is speaking: $isSpeaking")
                if (isSpeaking) {
                    Toast.makeText(this, "✅ Audio routing working!", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this, "❌ Audio routing failed", Toast.LENGTH_LONG).show()
                }
            }, 200)

            Toast.makeText(this, "🔊 Testing audio routing...", Toast.LENGTH_SHORT).show()
        }
    }

    private fun checkTTSStatus() {
        Log.d("TTSTest", "🔍 Checking TTS status...")

        val status = StringBuilder()
        status.append("TTS Initialized: ${if (isInitialized) "✅" else "❌"}\n")
        status.append("TTS Object: ${if (textToSpeech != null) "✅" else "❌"}\n")

        textToSpeech?.let { tts ->
            status.append("TTS Engine: Available\n")

            // Check if TTS is speaking
            val isSpeaking = tts.isSpeaking
            status.append("Currently Speaking: ${if (isSpeaking) "✅" else "❌"}\n")

            // Check language support
            val englishResult = tts.setLanguage(Locale.ENGLISH)
            val germanResult = tts.setLanguage(Locale.GERMAN)
            status.append("English Support: ${if (englishResult == TextToSpeech.SUCCESS) "✅" else "❌"}\n")
            status.append("German Support: ${if (germanResult == TextToSpeech.SUCCESS) "✅" else "❌"}\n")
        }

        statusText.text = status.toString()
        Toast.makeText(this, "Status updated", Toast.LENGTH_SHORT).show()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        textToSpeech?.let { tts ->
            tts.stop()
            tts.shutdown()
        }
        Log.d("TTSTest", "🛑 TTS destroyed")
    }
}
