<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        .test-button:hover {
            background: #45a049;
        }
        .status {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #2196F3;
        }
        .warning {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔊 TTS Test Page</h1>
        
        <div id="status" class="status">
            <strong>TTS Status:</strong> <span id="tts-status">Checking...</span>
        </div>
        
        <div id="german-warning" class="warning" style="display: none;">
            <strong>⚠️ German TTS Not Available</strong><br>
            To enable German pronunciation, please install the German language pack:
            <br><br>
            <button class="test-button" onclick="installGermanTTS()">📥 Install German Language Pack</button>
        </div>
        
        <h2>Test English TTS</h2>
        <button class="test-button" onclick="testEnglish()">🇺🇸 Say "Hello, how are you?"</button>
        
        <h2>Test German TTS</h2>
        <button class="test-button" onclick="testGerman()">🇩🇪 Say "Guten Tag, wie geht es dir?"</button>
        
        <h2>Test Words</h2>
        <button class="test-button" onclick="testGermanWord()">🇩🇪 Say "Danke"</button>
        <button class="test-button" onclick="testEnglishWord()">🇺🇸 Say "Thank you"</button>
        
        <div class="status">
            <strong>How it works:</strong><br>
            🇺🇸 <strong>English:</strong> Uses native Android TTS (works immediately)<br>
            🇩🇪 <strong>German:</strong> Uses native Android TTS (requires German language pack)<br>
            <br>
            <strong>For German TTS:</strong><br>
            1. If German pack is installed → Works immediately ✅<br>
            2. If not installed → App will prompt you to install<br>
            3. Tap "Yes" to open TTS settings automatically<br>
            4. Download German language pack and return to app
        </div>
    </div>

    <script>
        function testEnglish() {
            console.log('Testing English TTS');
            speakText("Hello, how are you?", "en-US");
        }
        
        function testGerman() {
            console.log('Testing German TTS');
            speakText("Guten Tag, wie geht es dir?", "de-DE");
        }
        
        function testGermanWord() {
            console.log('Testing German word');
            speakText("Danke", "de-DE");
        }
        
        function testEnglishWord() {
            console.log('Testing English word');
            speakText("Thank you", "en-US");
        }
        
        function installGermanTTS() {
            if (typeof AndroidTTS !== 'undefined') {
                AndroidTTS.installGermanTTS();
            } else {
                alert('Please go to Settings → Language & Input → Text-to-speech to install German');
            }
        }
        
        // Check TTS status when page loads
        setTimeout(function() {
            if (typeof AndroidTTS !== 'undefined') {
                const status = AndroidTTS.getTTSStatus();
                document.getElementById('tts-status').textContent = status;
                
                if (!AndroidTTS.isGermanTTSSupported()) {
                    document.getElementById('german-warning').style.display = 'block';
                }
            } else {
                document.getElementById('tts-status').textContent = 'Android TTS not available';
            }
        }, 500);
        
        // Default speakText function (will be overridden by Android)
        function speakText(text, lang) {
            console.log('Default speakText called:', text, lang);
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = lang;
                utterance.rate = 0.9;
                speechSynthesis.speak(utterance);
            } else {
                alert('Text-to-speech not supported');
            }
        }
    </script>
</body>
</html>
